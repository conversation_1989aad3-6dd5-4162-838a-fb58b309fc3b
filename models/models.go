package models

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/redis/go-redis/v9"
	"gorm.io/driver/postgres"
	_ "gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
	"log"
	"openedu-blockchain/cache_clients"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/pkg/util"
	"os"
	"reflect"
	"strings"
	"time"
)

var Redis *redis.Client
var DB *gorm.DB
var Cache *cache_clients.Caching
var Repository *AppRepository
var PublicSchema = "public"

type Model struct {
	ID       string `gorm:"primaryKey;type:varchar(20);unique" json:"id"`
	CreateAt int64  `gorm:"type:int8;not null;default:0" json:"create_at"`
	UpdateAt int64  `gorm:"type:int8;not null;default:0" json:"update_at"`
	DeleteAt int64  `gorm:"type:int8;not null;default:0" json:"delete_at"`
}

func IsRecordNotFound(err error) bool {
	return errors.Is(err, gorm.ErrRecordNotFound)
}

func GetDb(tbl TableName) *gorm.DB {
	return DB.Table(PublicSchema + "." + setting.DatabaseSetting.TablePrefix + string(tbl))
}

func GetTblName(tbl TableName) string {
	return PublicSchema + "." + setting.DatabaseSetting.TablePrefix + string(tbl)
}

// Setup initializes the database instance
func Setup() {
	var err error
	dsnConn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s",
		setting.DatabaseSetting.Host,
		setting.DatabaseSetting.User,
		setting.DatabaseSetting.Password,
		setting.DatabaseSetting.Name,
		setting.DatabaseSetting.Port,
		setting.DatabaseSetting.SSLMode)
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
		logger.Config{
			SlowThreshold:             time.Second,            // Slow SQL threshold
			LogLevel:                  logger.Silent,          // Log level
			IgnoreRecordNotFoundError: true,                   // Ignore ErrRecordNotFound error for logger
			ParameterizedQueries:      setting.IsProduction(), // Don't include params in the SQL log
			Colorful:                  true,                   // Disable color
		},
	)

	db, err := gorm.Open(postgres.New(postgres.Config{
		DSN:                  dsnConn,
		PreferSimpleProtocol: true, // disables implicit prepared statement usage
	}), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   setting.DatabaseSetting.TablePrefix, // table name prefix, table for `User` would be `t_users`
			SingularTable: false,                               // use singular table name, table for `User` would be `user` with this option enabled
			NoLowerCase:   false,                               // skip the snake_casing of names
			NameReplacer:  strings.NewReplacer("CID", "Cid"),   // use name replacer to change struct/field name before convert it to DB name
		},
		//Logger: logger.Default.LogMode(logger.Info),
		Logger: newLogger,
	})
	if err != nil {
		log.Fatalf("models.Setup failed to connect PostgreSQL: %v", err)
	}

	if err = db.Callback().Create().Before("gorm:create").Register("before_create", beforeCreate); err != nil {
		log.Fatalf("models.Setup failed to register callback beforeCreate: %v", err)
	}

	if err = db.Callback().Update().Before("gorm:update").Register("before_update", beforeUpdate); err != nil {
		log.Fatalf("models.Setup failed to register callback beforeUpdate: %v", err)
	}

	sDB, err := db.DB()
	if err != nil {
		log.Fatalf("models.Setup failed to get sql.DB: %v", err)
	}
	sDB.SetMaxIdleConns(10)
	sDB.SetMaxOpenConns(100)
	DB = db

	// Init Redis instance
	Redis = redis.NewClient(&redis.Options{
		Addr:            setting.RedisSetting.Host,
		Password:        setting.RedisSetting.Password,
		DB:              0, // use default DB
		MaxIdleConns:    setting.RedisSetting.MaxIdle,
		MaxActiveConns:  setting.RedisSetting.MaxActive,
		ConnMaxIdleTime: setting.RedisSetting.IdleTimeout,
	})
	ctxRedis, cancelRedis := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancelRedis()
	_, err = Redis.Ping(ctxRedis).Result()
	if err != nil {
		log.Fatalf("Failed to ping to Redis: %v", err)
	}

	Repository = &AppRepository{
		Wallet:        &WalletRepository{},
		UserSetting:   &UserSettingRepository{},
		System:        &SystemConfigRepository{},
		Transaction:   &TransactionRepository{},
		SponsorWallet: &SponsorWalletRepository{},
	}

	Cache = &cache_clients.Caching{
		System: &cache_clients.SystemConfigCache{CacheModel: cache_clients.CacheModel{Prefix: setting.DatabaseSetting.TablePrefix + SystemConfigCachePrefix}},
	}
}

func beforeCreate(db *gorm.DB) {
	if db.Statement.Schema != nil {
		switch db.Statement.ReflectValue.Kind() {
		case reflect.Struct:
			rv := db.Statement.ReflectValue
			db.AddError(applyPrimaryKeyAndTimestampOnCreate(db, rv))

		case reflect.Slice, reflect.Array:
			for i := 0; i < db.Statement.ReflectValue.Len(); i++ {
				rv := db.Statement.ReflectValue.Index(i)
				if reflect.Indirect(rv).Kind() != reflect.Struct {
					break
				}

				db.AddError(applyPrimaryKeyAndTimestampOnCreate(db, rv))
			}
		default:
			return
		}
	}
}

func beforeUpdate(db *gorm.DB) {
	if db.Statement.Schema != nil {
		switch db.Statement.ReflectValue.Kind() {
		case reflect.Struct:
			rv := db.Statement.ReflectValue
			db.AddError(applyPrimaryKeyAndTimestampOnUpdate(db, rv))

		case reflect.Slice, reflect.Array:
			for i := 0; i < db.Statement.ReflectValue.Len(); i++ {
				rv := db.Statement.ReflectValue.Index(i)
				if reflect.Indirect(rv).Kind() != reflect.Struct {
					break
				}

				db.AddError(applyPrimaryKeyAndTimestampOnUpdate(db, rv))
			}

		default:
			return
		}

		return
	}
}

func applyPrimaryKeyAndTimestampOnCreate(db *gorm.DB, rv reflect.Value) error {
	dbContext := db.Statement.Context

	if db.Statement.Schema.PrioritizedPrimaryField != nil {
		_, isZero := db.Statement.Schema.PrioritizedPrimaryField.ValueOf(dbContext, rv)
		if isZero {
			err := db.Statement.Schema.PrioritizedPrimaryField.Set(dbContext, rv, util.GenerateId())
			if err != nil {
				return err
			}
		}
	}

	createAtField := db.Statement.Schema.LookUpField("create_at")
	if createAtField != nil {
		err := createAtField.Set(dbContext, rv, time.Now().UnixMilli())
		if err != nil {
			return err
		}
	}

	updateAtField := db.Statement.Schema.LookUpField("update_at")
	if updateAtField != nil {
		err := updateAtField.Set(dbContext, rv, time.Now().UnixMilli())
		if err != nil {
			return err
		}
	}

	deleteAtField := db.Statement.Schema.LookUpField("delete_at")
	if deleteAtField != nil {
		err := deleteAtField.Set(dbContext, rv, 0)
		if err != nil {
			return err
		}
	}

	return nil
}

func applyPrimaryKeyAndTimestampOnUpdate(db *gorm.DB, rv reflect.Value) error {
	dbContext := db.Statement.Context

	updateAtField := db.Statement.Schema.LookUpField("update_at")
	if updateAtField != nil {
		err := updateAtField.Set(dbContext, rv, time.Now().UnixMilli())
		if err != nil {
			return err
		}
	}

	return nil
}

type JSONB map[string]interface{}

func (j JSONB) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *JSONB) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &j); err != nil {
		return err
	}
	return nil
}

type JSONArray []interface{}

func (j JSONArray) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *JSONArray) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &j); err != nil {
		return err
	}
	return nil
}

type StringArray []string

func (p StringArray) Value() (driver.Value, error) {
	val, err := json.Marshal(p)
	return string(val), err
}

func (p *StringArray) Scan(src interface{}) error {
	source, ok := src.([]byte)
	if !ok {
		return errors.New("type assertion .([]byte) failed")
	}

	err := json.Unmarshal(source, &p)
	if err != nil {
		return err
	}

	return nil
}
