package models

import (
	"github.com/samber/lo"
	"math"
)

type Pagination struct {
	Page       int `json:"page"`
	PerPage    int `json:"per_page"`
	TotalPages int `json:"total_pages"`
	TotalItems int `json:"total_items"`
}

func NewPagination(page, perPage, totalItems int) *Pagination {
	return &Pagination{
		Page:    page,
		PerPage: perPage,
		TotalPages: lo.If(totalItems == 0, 0).
			Else(int(math.Ceil(float64(totalItems) / float64(perPage)))),
		TotalItems: totalItems,
	}
}
