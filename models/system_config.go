package models

import (
	"encoding/json"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

const (
	AllSystemConfig = "all_system_config"
)

type SystemConfig struct {
	Model
	Key      string `json:"key"`
	DataType string `json:"data_type"`
	Value    string `json:"value" gorm:"type:text"`
	OrgID    string `json:"org_id" gorm:"type:varchar(20)"`
}

type SimpleConfig struct {
	Model
	Key      string `json:"key"`
	Value    any    `json:"value"`
	OrgID    string `json:"org_id"`
	DataType string `json:"data_type"`
}

func (cfg *SystemConfig) GetKey() string {
	return cfg.Key
}

type SystemConfigQuery struct {
	ID             *string `form:"id"`
	Key            *string `form:"key"`
	OrgID          *string `form:"org_id"`
	IncludeDeleted *bool   `form:"include_deleted"`
}

func (query SystemConfigQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.Key != nil {
		qb = qb.Where("key = ?", *query.Key)
	}

	if (query.IncludeDeleted != nil) && !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *SystemConfigRepository) Upsert(c *SystemConfig) error {
	var s *SystemConfig
	q := GetDb(SystemConfigTbl).
		Where(SystemConfig{Key: c.Key, OrgID: c.OrgID}).
		Assign(SystemConfig{Value: c.Value, OrgID: c.OrgID, DataType: c.DataType}).
		FirstOrCreate(&s)
	if q.Error != nil {
		return q.Error
	}

	if cErr := Cache.System.Delete(AllSystemConfig); cErr != nil {
		log.Errorf("Delete cache for all system config failed: %v", cErr)
	}

	return nil
}

func (r *SystemConfigRepository) FindAll() ([]*SystemConfig, error) {
	var systemConfigs []*SystemConfig
	var configCache []interface{}
	if cErr := Cache.System.GetAll(AllSystemConfig, &configCache); cErr == nil && len(configCache) > 0 {
		if cErr = Cache.Convert(configCache, &systemConfigs); cErr != nil {
			log.Errorf("Convert cache value to system configs failed: %v", cErr)
		}
	}

	if len(systemConfigs) > 0 {
		return systemConfigs, nil
	}

	// If not found in cache, retrieve from database
	data, err := findMany[SystemConfig](SystemConfigTbl, &SystemConfigQuery{}, nil)
	if err != nil {
		return nil, err
	}

	// Update cache with the retrieved data
	cacheItems := lo.Map(data, func(sysCfg *SystemConfig, _ int) interface{} {
		return sysCfg
	})

	if cErr := Cache.System.SetAll(AllSystemConfig, cacheItems); cErr != nil {
		log.Errorf("Set cache for all system config failed: %v", cErr)
	}

	return data, nil
}

func (r *SystemConfigRepository) FindByKey(key string) (*SystemConfig, error) {
	sysCfgs, err := r.FindAll()
	if err != nil {
		return nil, err
	}

	sysCfg, found := lo.Find(sysCfgs, func(cfg *SystemConfig) bool {
		return cfg.Key == key
	})
	if !found {
		return nil, gorm.ErrRecordNotFound
	}

	return sysCfg, nil
}

func (r *SystemConfigRepository) FindOne(query *SystemConfigQuery, options *FindOneOptions) (*SystemConfig, error) {
	return findOne[SystemConfig](SystemConfigTbl, query, options)
}

func InitConfig[T any](key string, value T, dataType string, orgID *string) error {
	jsonData, err := json.Marshal(value)
	if err != nil {
		return err
	}
	if f, _ := Repository.System.FindOne(&SystemConfigQuery{Key: util.NewString(key), OrgID: lo.If(orgID != nil, orgID).Else(nil)}, nil); f == nil {
		log.Debug("string(jsonData): ", key, string(jsonData))
		sysConfig := &SystemConfig{Value: string(jsonData), Key: key, DataType: dataType}
		if dataType == String {
			if str, ok := any(value).(string); ok {
				sysConfig.Value = str
			}
		}
		if orgID != nil {
			sysConfig.OrgID = *orgID
		}
		if sErr := Repository.System.Upsert(sysConfig); sErr != nil {
			return sErr
		}
	}
	return nil
}

func GetConfig[T any](key string) T {
	var result T
	if all, err := Repository.System.FindAll(); err != nil {
		return result
	} else {
		config, found := lo.Find(all, func(item *SystemConfig) bool {
			return item.Key == key
		})
		if !found {
			return result
		}

		// Handle different types based on T
		switch any(result).(type) {
		case string:
			// Return the config value directly as string
			return any(config.Value).(T)
		default:
			// Try to unmarshal into the expected type
			uErr := json.Unmarshal([]byte(config.Value), &result)
			if uErr != nil {
				return result
			}
			return result
		}
	}
}

func (r *SystemConfigRepository) Create(c *SystemConfig, trans *gorm.DB) error {
	if cErr := Cache.System.Delete(AllSystemConfig); cErr != nil {
		log.Errorf("Delete cache for all system config failed: %v", cErr)
	}
	return create(SystemConfigTbl, c, trans)
}

func (r *SystemConfigRepository) CreateMany(c []*SystemConfig, trans *gorm.DB) error {
	return createMany(SystemConfigTbl, c, trans)
}

func (r *SystemConfigRepository) FindByID(id string, options *FindOneOptions) (*SystemConfig, error) {
	return findByID[SystemConfig](SystemConfigTbl, id, options)
}

func (r *SystemConfigRepository) FindMany(query *SystemConfigQuery, options *FindManyOptions) ([]*SystemConfig, error) {
	return findMany[SystemConfig](SystemConfigTbl, query, options)
}

func (r *SystemConfigRepository) FindPage(query *SystemConfigQuery, options *FindPageOptions) ([]*SystemConfig, *Pagination, error) {
	return findPage[SystemConfig](SystemConfigTbl, query, options)
}

func (r *SystemConfigRepository) Update(c *SystemConfig, trans *gorm.DB) error {
	if cErr := Cache.System.Delete(AllSystemConfig); cErr != nil {
		log.Errorf("Delete cache for all system config failed: %v", cErr)
	}
	return update(SystemConfigTbl, c, trans)
}

func (r *SystemConfigRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[SystemConfig](SystemConfigTbl, id, trans)
}

func (r *SystemConfigRepository) DeleteMany(query *SystemConfigQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[SystemConfig](SystemConfigTbl, query, trans)
}
