package models

import (
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"openedu-blockchain/pkg/kms"
	"openedu-blockchain/pkg/setting"
)

type Wallet struct {
	Model
	UserID              string            `json:"user_id"`
	Address             string            `json:"address"`
	PublicKey           string            `json:"public_key"`
	EncryptedPrivateKey string            `json:"encrypted_private_key"`
	Network             BlockchainNetwork `json:"network"`
	Status              WalletStatus      `json:"status"`
	CoreWalletID        string            `json:"core_wallet_id"` // CoreWalletID is unique
}

func (t BlockchainNetwork) String() string {
	return string(t)
}

func (t WalletStatus) String() string {
	return string(t)
}

func IsValidWalletType(walletType BlockchainNetwork) bool {
	return lo.Contains([]BlockchainNetwork{NetworkNEAR, NetworkAVAIL, NetworkBASE}, walletType)
}

func TokenID2Token(tokenID string) BlockchainToken {
	switch tokenID {
	case setting.NearSetting.MainnetUSDTContractID,
		setting.NearSetting.TestnetUSDTContractID:
		return TokenUSDT

	case setting.NearSetting.MainnetUSDCContractID,
		setting.NearSetting.TestnetUSDCContractID:
		return TokenUSDC

	case setting.NearSetting.MainnetOpenEduContractID,
		setting.NearSetting.TestnetOpenEduContractID:
		return TokenOPENEDU

	default:
		return BlockchainToken(tokenID)
	}
}

func (w *Wallet) IsNetworkNEAR() bool {
	return w.Network == NetworkNEAR
}

func (w *Wallet) GetNetwork() BlockchainNetwork {
	return w.Network
}

func (w *Wallet) GetAddress() string {
	return w.Address
}

func (w *Wallet) GetPublicKey() string {
	return w.PublicKey
}

func (w *Wallet) GetPrivateKey() (string, error) {
	return kms.Decrypt(w.EncryptedPrivateKey)
}

type SimpleWallet struct {
	Model
	UserID       string            `json:"user_id"`
	Address      string            `json:"address"`
	PublicKey    string            `json:"public_key"`
	Network      BlockchainNetwork `json:"network"`
	Status       WalletStatus      `json:"status"`
	Balance      decimal.Decimal   `json:"balance"`
	CoreWalletID string            `json:"core_wallet_id"`
}

func (w *Wallet) ToSimple() *SimpleWallet {
	return &SimpleWallet{
		Model:        w.Model,
		UserID:       w.UserID,
		Address:      w.Address,
		PublicKey:    w.PublicKey,
		Network:      w.Network,
		Status:       w.Status,
		CoreWalletID: w.CoreWalletID,
	}
}

type WalletQuery struct {
	ID        *string            `json:"id" form:"id"`
	IDIn      []string           `json:"id_in" form:"id_in"`
	UserID    *string            `json:"user_id" form:"user_id"`
	AddressIn []string           `json:"address_in" form:"address_in"`
	Network   *BlockchainNetwork `json:"network" form:"network"`
}

func (query *WalletQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if len(query.AddressIn) > 0 {
		qb = qb.Where("address IN (?)", query.AddressIn)
	}

	if query.Network != nil {
		qb = qb.Where("network = ?", *query.Network)
	}

	return qb
}

func (r *WalletRepository) Create(wallet *Wallet, trans *gorm.DB) error {
	return create(WalletTbl, wallet, trans)
}

func (r *WalletRepository) CreateMany(wallets []*Wallet, trans *gorm.DB) error {
	return createMany(WalletTbl, wallets, trans)
}

func (r *WalletRepository) Update(wallet *Wallet, trans *gorm.DB) error {
	return update(WalletTbl, wallet, trans)
}

func (r *WalletRepository) FindByID(id string, options *FindOneOptions) (*Wallet, error) {
	return findByID[Wallet](WalletTbl, id, options)
}

func (r *WalletRepository) FindOne(query *WalletQuery, options *FindOneOptions) (*Wallet, error) {
	return findOne[Wallet](WalletTbl, query, options)
}

func (r *WalletRepository) FindPage(query *WalletQuery, options *FindPageOptions) ([]*Wallet, *Pagination, error) {
	return findPage[Wallet](WalletTbl, query, options)
}

func (r *WalletRepository) FindMany(query *WalletQuery, options *FindManyOptions) ([]*Wallet, error) {
	return findMany[Wallet](WalletTbl, query, options)
}

func (r *WalletRepository) Count(query *WalletQuery) (int64, error) {
	return count[Wallet](WalletTbl, query)
}

func (r *WalletRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Wallet](WalletTbl, id, trans)
}
