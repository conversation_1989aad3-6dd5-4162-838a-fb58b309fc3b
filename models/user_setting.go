package models

import (
	"fmt"
	"gorm.io/gorm"
)

type UserSetting struct {
	UserID              string `json:"user_id" gorm:"primary_key,type:varchar(20)"`
	EncryptedSeedPhrase string `json:"encrypted_seed_phrase"`
	EncryptedSecret     string `json:"encrypted_secret"`
}

type UserSettingQuery struct {
	ID     *string   `json:"id" form:"id"`
	IDIn   []*string `json:"id_in" form:"id_in"`
	UserID *string   `json:"user_id" form:"user_id"`
}

func (s *UserSetting) HasSecret() bool {
	return s.EncryptedSecret != ""
}

func (s *UserSetting) HasSeedPhrase() bool {
	return s.EncryptedSeedPhrase != ""
}

func (query *UserSettingQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	return qb
}

func (r *UserSettingRepository) Create(setting *UserSetting, trans *gorm.DB) error {
	return create(UserSettingTbl, setting, trans)
}

func (r *UserSettingRepository) CreateMany(settings []*UserSetting, trans *gorm.DB) error {
	return createMany(UserSettingTbl, settings, trans)
}

func (r *UserSettingRepository) Update(setting *UserSetting, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblName(UserSettingTbl)).Debug().
		Select("*").
		Omit("user_id").
		Where("user_id = ?", setting.UserID).
		Updates(&setting).Error
	return
}

func (r *UserSettingRepository) FindByUserID(userId string, options *FindOneOptions) (*UserSetting, error) {
	var setting UserSetting
	qb := ApplyQueryOptions(DB, options)
	err := qb.Table(GetTblName(UserSettingTbl)).Debug().Where("user_id = ?", userId).First(&setting).Error
	if err != nil {
		return nil, err
	}

	return &setting, nil
}

func (r *UserSettingRepository) FindOne(query *UserSettingQuery, options *FindOneOptions) (*UserSetting, error) {
	return findOne[UserSetting](UserSettingTbl, query, options)
}

func (r *UserSettingRepository) FindPage(query *UserSettingQuery, options *FindPageOptions) ([]*UserSetting, *Pagination, error) {
	return findPage[UserSetting](UserSettingTbl, query, options)
}

func (r *UserSettingRepository) FindMany(query *UserSettingQuery, options *FindManyOptions) ([]*UserSetting, error) {
	return findMany[UserSetting](UserSettingTbl, query, options)
}

func (r *UserSettingRepository) Count(query *UserSettingQuery) (int64, error) {
	return count[UserSetting](UserSettingTbl, query)
}

func (r *UserSettingRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[UserSetting](UserSettingTbl, id, trans)
}
