package models

import "time"

type TableName string

type TransferType string

type BlockchainNetwork string

type WalletStatus string

type TransactionType string

type TransactionStatus string

type BlockchainToken string

type QueryType string

type Participant string

const (
	String  string = "string"
	JsonB   string = "jsonb"
	JsonArr string = "json_array"

	SystemConfigCachePrefix = "system_config_"

	DefaultDelay = 200 * time.Millisecond

	NetworkNEAR  BlockchainNetwork = "near"
	NetworkAVAIL BlockchainNetwork = "avail"
	NetworkBASE  BlockchainNetwork = "base"

	NEAR2NEAR   TransferType = "NEAR2NEAR"
	AVAIL2AVAIL TransferType = "AVAIL2AVAIL"
	ETH2ETH     TransferType = "ETH2ETH"

	WalletStatusActive   WalletStatus = "active"
	WalletStatusInactive WalletStatus = "inactive"

	QueryTypeWalletEarnings          QueryType = "earnings"
	QueryTypeWalletGasSponsorBalance QueryType = "gas_sponsor_balance"
	QueryTypeLaunchpadVotingPowers   QueryType = "launchpad_voting_powers"
	QueryTypeWalletAccountInfo       QueryType = "wallet_account_info"
	QueryTypeSponsorWalletExists     QueryType = "sponsor_wallet_exists"
	QueryTypeGetSponsorWallets       QueryType = "get_sponsor_wallets"
	QueryTypeGetSponsorWalletByID    QueryType = "get_sponsor_wallet_by_id"

	TxnTypeTransfer                   TransactionType = "transfer"
	TxnTypeMintNFT                    TransactionType = "mint_nft"
	TxnTypePayment                    TransactionType = "payment"
	TxnTypeClaimEarning               TransactionType = "claim_earning"
	TxnTypeBatchTransfer              TransactionType = "batch_transfer"
	TxnTypeDepositSponsorGas          TransactionType = "deposit_sponsor_gas"
	TxnTypeWithdrawSponsorGas         TransactionType = "withdraw_sponsor_gas"
	TxnTypeInitSponsorWallet          TransactionType = "init_sponsor_wallet"
	TxnInitLaunchpadPool              TransactionType = "init_launchpad_pool"
	TxnApproveLaunchpadPool           TransactionType = "approve_launchpad_pool"
	TxnUpdateLaunchpadPoolStatus      TransactionType = "update_launchpad_pool_status"
	TxnPledgeLaunchpad                TransactionType = "pledge_launchpad"
	TxnUpdateLpPoolFundingTime        TransactionType = "update_launchpad_funding_time"
	TxnCancelLaunchpad                TransactionType = "cancel_launchpad"
	TxnCheckLpFundingResult           TransactionType = "check_launchpad_funding_result"
	TxnContinueLpPartialFund          TransactionType = "continue_launchpad_partial_fund"
	TxnSetLpPoolFundingTime           TransactionType = "set_launchpad_funding_time"
	TxnWithdrawLaunchpadFundToCreator TransactionType = "withdraw_launchpad_fund_to_creator"
	TxnTypeClaimLaunchpadRefund       TransactionType = "claim_launchpad_refund"

	TxStatusPending TransactionStatus = "pending"
	TxStatusSuccess TransactionStatus = "success"
	TxStatusFailed  TransactionStatus = "failed"

	TokenNEAR    BlockchainToken = "NEAR"
	TokenAVAIL   BlockchainToken = "AVAIL"
	TokenUSDT    BlockchainToken = "USDT"
	TokenUSDC    BlockchainToken = "USDC"
	TokenOPENEDU BlockchainToken = "OPENEDU"
	TokenETH     BlockchainToken = "ETH"

	WalletTbl       TableName = "wallets"
	UserSettingTbl  TableName = "user_settings"
	SystemConfigTbl TableName = "system_configs"
	TransactionTbl  TableName = "transactions"

	Platform  Participant = "platform"
	Creator   Participant = "creator"
	Learner   Participant = "learner"
	Paymaster Participant = "paymaster"
)
