package models

import (
	"fmt"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"reflect"
	"time"
)

type QueryOptions interface {
	Apply(tx *gorm.DB) *gorm.DB
}

type FindOneOptions struct {
	Preloads       []string                           `json:"preloads" form:"preloads"`
	CustomPreloads map[string]func(*gorm.DB) *gorm.DB `json:"custom_preloads" form:"-"`
	Sort           []string                           `json:"sort" form:"sort"`
}

func (o *FindOneOptions) Apply(tx *gorm.DB) *gorm.DB {
	qb := tx
	lo.ForEach(o.Preloads, func(clause string, _ int) {
		qb = qb.Preload(clause)
	})

	for field, preloadFn := range o.CustomPreloads {
		qb = qb.Preload(field, preloadFn)
	}

	lo.ForEach(o.Sort, func(clause string, _ int) {
		qb = qb.Order(clause)
	})

	return qb
}

type FindManyOptions struct {
	Preloads       []string                           `json:"preloads" form:"preloads"`
	CustomPreloads map[string]func(*gorm.DB) *gorm.DB `json:"custom_preloads" form:"-"`
	Sort           []string                           `json:"sort" form:"sort"`
	Limit          *int                               `json:"limit" form:"limit"`
	Offset         *int                               `json:"offset" form:"offset"`
}

func (o *FindManyOptions) Apply(tx *gorm.DB) *gorm.DB {
	qb := tx
	lo.ForEach(o.Preloads, func(clause string, _ int) {
		qb = qb.Preload(clause)
	})

	for field, preloadFn := range o.CustomPreloads {
		qb = qb.Preload(field, preloadFn)
	}

	lo.ForEach(o.Sort, func(clause string, _ int) {
		qb = qb.Order(clause)
	})

	if o.Limit != nil {
		qb = qb.Limit(*o.Limit)
	}

	if o.Offset != nil {
		qb = qb.Offset(*o.Offset)
	}

	return qb
}

type FindPageOptions struct {
	Preloads       []string                           `json:"preloads" form:"preloads"`
	CustomPreloads map[string]func(*gorm.DB) *gorm.DB `json:"custom_preloads" form:"-"`
	Sort           []string                           `json:"sort"  form:"sort"`
	Page           int                                `json:"page" form:"page"`
	PerPage        int                                `json:"per_page" form:"per_page"`
}

func (o *FindPageOptions) Apply(tx *gorm.DB) *gorm.DB {
	qb := tx
	lo.ForEach(o.Preloads, func(clause string, _ int) {
		qb = qb.Preload(clause)
	})

	for field, preloadFn := range o.CustomPreloads {
		qb = qb.Preload(field, preloadFn)
	}

	lo.ForEach(o.Sort, func(clause string, _ int) {
		qb = qb.Order(clause)
	})

	limit := o.PerPage
	offset := (o.Page - 1) * o.PerPage
	qb = qb.Limit(limit).Offset(offset)
	return qb
}

func ApplyQueryOptions(tx *gorm.DB, queries ...QueryOptions) *gorm.DB {
	qb := tx
	for _, query := range queries {
		if !reflect.ValueOf(query).IsNil() {
			qb = query.Apply(qb)
		}
	}
	return qb
}

func create[T any](tblName TableName, entity T, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()
	err = tx.Table(GetTblName(tblName)).Debug().Create(&entity).Error
	return
}

func createMany[T any](tbl TableName, entities []T, trans *gorm.DB) (err error) {
	if len(entities) == 0 {
		return nil
	}

	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblName(tbl)).Debug().Create(&entities).Error
	return
}

func findByID[T any](tbl TableName, id string, options *FindOneOptions) (*T, error) {
	var entity T
	qb := ApplyQueryOptions(DB, options)
	err := qb.Table(GetTblName(tbl)).Debug().Where("id = ? AND delete_at = 0", id).First(&entity).Error
	if err != nil {
		return nil, err
	}

	return &entity, nil
}

func findOne[T any](tbl TableName, query QueryOptions, options *FindOneOptions) (*T, error) {
	var entity T
	qb := ApplyQueryOptions(DB, query, options)
	err := qb.Table(GetTblName(tbl)).Debug().First(&entity).Error
	if err != nil {
		return nil, err
	}

	return &entity, nil
}

func findMany[T any](tbl TableName, query QueryOptions, options *FindManyOptions) ([]*T, error) {
	var entities []*T
	qb := ApplyQueryOptions(DB, query, options)
	err := qb.Table(GetTblName(tbl)).Debug().Find(&entities).Error
	if err != nil {
		return nil, err
	}

	return entities, nil
}

func findPage[T any](tbl TableName, query QueryOptions, options *FindPageOptions) (entities []*T, pagination *Pagination, err error) {
	entitiesChan := make(chan []*T)
	countChan := make(chan int64)
	errorChan := make(chan error)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		qb := ApplyQueryOptions(DB, query, options)
		result := qb.Table(GetTblName(tbl)).Debug().Find(&entities)
		if fErr := result.Error; fErr != nil {
			errorChan <- fErr
			return
		}
		entitiesChan <- entities
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		c, cErr := count[T](tbl, query)
		if cErr != nil {
			errorChan <- cErr
			return
		}
		countChan <- c
	}()

	// Wait for all goroutines to finish
	var entityCount int64
	for i := 0; i < 2; i++ {
		select {
		case entities = <-entitiesChan:
		case entityCount = <-countChan:
		case err = <-errorChan:
			return nil, nil, err
		}
	}

	return entities, NewPagination(options.Page, options.PerPage, int(entityCount)), nil
}

func update[T any](tbl TableName, entity T, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblName(tbl)).Debug().Select("*").Omit("id", "create_at").Updates(&entity).Error
	return
}

func deleteByID[T any](tbl TableName, id string, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	var entity T
	now := time.Now().UnixMilli()
	result := tx.Table(GetTblName(tbl)).Debug().
		Model(&entity).
		Where("id = ? AND delete_at = 0", id).
		Updates(map[string]interface{}{
			"delete_at": now,
			//"update_at": now,
		})

	err = result.Error
	if result.RowsAffected == 0 {
		err = lo.If(err == nil, gorm.ErrRecordNotFound).Else(err)
	}
	return
}

func deleteMany[T any](tbl TableName, query QueryOptions, trans *gorm.DB) (count int64, err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	qb := ApplyQueryOptions(tx, query)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	var entity T
	now := time.Now().UnixMilli()
	result := qb.Table(GetTblName(tbl)).Debug().
		Model(&entity).
		Updates(map[string]interface{}{
			"delete_at": now,
			//"update_at": now,
		})

	err = result.Error
	count = result.RowsAffected
	return
}

func count[T any](tbl TableName, query QueryOptions) (c int64, err error) {
	if r := recover(); r != nil {
		err = fmt.Errorf("panic: %v", r)
	}

	var entity T
	qb := ApplyQueryOptions(DB, query)
	result := qb.Table(GetTblName(tbl)).Debug().Model(&entity).Count(&c)
	if err = result.Error; err != nil {
		return -1, err
	}
	return c, nil
}
