package models

import "gorm.io/gorm"

type AppRepository struct {
	Wallet        WalletRepositoryIface
	UserSetting   UserSettingRepositoryIface
	System        SystemConfigRepositoryIface
	Transaction   TransactionRepositoryIface
	SponsorWallet SponsorWalletRepositoryIface
}

type WalletRepository struct{}

type UserSettingRepository struct{}

type SystemConfigRepository struct{}

type TransactionRepository struct{}

type WalletRepositoryIface interface {
	Create(wallet *Wallet, trans *gorm.DB) error
	CreateMany(wallets []*Wallet, trans *gorm.DB) error
	Update(wallet *Wallet, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*Wallet, error)
	FindOne(query *WalletQuery, options *FindOneOptions) (*Wallet, error)
	FindPage(query *WalletQuery, options *FindPageOptions) ([]*Wallet, *Pagination, error)
	FindMany(query *WalletQuery, options *FindManyOptions) ([]*Wallet, error)
	Count(query *WalletQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
}

type UserSettingRepositoryIface interface {
	Create(setting *UserSetting, trans *gorm.DB) error
	CreateMany(settings []*UserSetting, trans *gorm.DB) error
	Update(setting *UserSetting, trans *gorm.DB) error
	FindByUserID(userId string, options *FindOneOptions) (*UserSetting, error)
	FindOne(query *UserSettingQuery, options *FindOneOptions) (*UserSetting, error)
	FindPage(query *UserSettingQuery, options *FindPageOptions) ([]*UserSetting, *Pagination, error)
	FindMany(query *UserSettingQuery, options *FindManyOptions) ([]*UserSetting, error)
	Count(query *UserSettingQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
}

type SystemConfigRepositoryIface interface {
	Upsert(c *SystemConfig) error
	FindAll() ([]*SystemConfig, error)
	FindByKey(key string) (*SystemConfig, error)
	FindOne(query *SystemConfigQuery, options *FindOneOptions) (*SystemConfig, error)
	Create(c *SystemConfig, trans *gorm.DB) error
	CreateMany(c []*SystemConfig, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*SystemConfig, error)
	FindMany(query *SystemConfigQuery, options *FindManyOptions) ([]*SystemConfig, error)
	FindPage(query *SystemConfigQuery, options *FindPageOptions) ([]*SystemConfig, *Pagination, error)
	Update(c *SystemConfig, trans *gorm.DB) error
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *SystemConfigQuery, trans *gorm.DB) (int64, error)
}

type TransactionRepositoryIface interface {
	Create(transaction *Transaction, trans *gorm.DB) error
	CreateMany(transactions []*Transaction, trans *gorm.DB) error
	Update(transaction *Transaction, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*Transaction, error)
	FindOne(query *TransactionQuery, options *FindOneOptions) (*Transaction, error)
	FindPage(query *TransactionQuery, options *FindPageOptions) ([]*Transaction, *Pagination, error)
	FindMany(query *TransactionQuery, options *FindManyOptions) ([]*Transaction, error)
	Count(query *TransactionQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
}

type SponsorWalletRepositoryIface interface {
	Create(sponsorWallet *SponsorWallet, trans *gorm.DB) error
	Update(sponsorWallet *SponsorWallet, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*SponsorWallet, error)
	FindOne(query *SponsorWalletQuery, options *FindOneOptions) (*SponsorWallet, error)
	FindPage(query *SponsorWalletQuery, options *FindPageOptions) ([]*SponsorWallet, *Pagination, error)
	FindMany(query *SponsorWalletQuery, options *FindManyOptions) ([]*SponsorWallet, error)
	Count(query *SponsorWalletQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
}
