version: '3.8'

services:
  rabbitmq:
    container_name: rabbitmq_openedu
    # There is a prebuilt RabbitMQ image; see
    # https://hub.docker.com/_/rabbitmq/ for details.
    # This variant is built on Alpine Linux (it's smaller) and includes
    # the management UI.
    image: 'rabbitmq:4.0.2-management-alpine'
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-open_user}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD:-open_password}
    volumes:
      - ./.data_storage/rabbitmq:/var/lib/rabbitmq

    # These ports are exposed on the host; 'hostport:containerport'.
    # You could connect to this server from outside with the *host's*
    # DNS name or IP address and port 5672 (the left-hand side of the
    # colon).
    ports:
      # The standard AMQP protocol port
      - "5672:5672"
      # HTTP management UI
      - "15672:15672"

    restart: unless-stopped

    # Run this container on a private network for this application.
    # This is necessary for magic Docker DNS to work: other containers
    # also running on this network will see a host name "rabbitmq"
    # (the name of this section) and the internal port 5672, even though
    # that's not explicitly published above.
    networks:
      - app_network

networks:
  app_network:
    driver: bridge
