package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/queue/consumer"
	"openedu-blockchain/services"
	"time"
)

const (
	LaunchpadRPCQueryWorkerName = "Queue.LaunchpadRPCQuery"
)

func HandleLaunchpadRPCQuery(ctx context.Context, workerID int, msgChan <-chan consumer.Message) {
	workerName := fmt.Sprintf("%s worker ID %d", LaunchpadRPCQueryWorkerName, workerID)
	defer log.Infof("%s stopped", workerName)
	for {
		select {
		case <-ctx.Done():
			return
		case msg, ok := <-msgChan:
			startTime := time.Now()
			if !ok {
				// Channel closed, stop the worker.
				log.Infof("%s stopping due to closed message channel", workerName)
				return
			}

			log.Infof("%s received message ID %s", worker<PERSON><PERSON>, msg.GetID())
			log.Debugf("%s message ID %s data: %s", workerName, msg.GetID(), string(msg.GetBody()))

			var req dto.RpcQueryRequest
			if err := json.Unmarshal(msg.GetBody(), &req); err != nil {
				handleError(startTime, workerName, msg, err)
				continue
			}

			switch req.Type {
			case models.QueryTypeLaunchpadVotingPowers:
				var votingPowerReq dto.GetVotingPowersRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &votingPowerReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				votingPowers, appErr := services.Launchpad.GetVotingPowersByPool(&votingPowerReq)
				if appErr != nil {
					handleErrorRPC(startTime, workerName, msg, appErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, votingPowers)

			default:
				appErr := e.NewError400(e.WalletInvalidRequest, "Invalid get details type: "+string(req.Type))
				handleErrorRPC(startTime, workerName, msg, appErr)
			}
		}
	}
}
