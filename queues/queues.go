package queues

import (
	"context"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/queue/consumer"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/queues/handlers"
)

const (
	WalletCreateQueueName      = "wallet_create_queue"
	WalletCreateWorkerPoolSize = 50

	SponsorWalletCreateQueueName      = "sponsor_wallet_create_queue"
	SponsorWalletCreateWorkerPoolSize = 50

	SponsorWalletRPCQueryQueueName      = "sponsor_wallet_rpc_query"
	SponsorWalletRPCQueryWorkerPoolSize = 50

	WalletRPCQueryQueueName      = "wallet_rpc_query"
	WalletRPCQueryWorkerPoolSize = 50

	LaunchpadRPCQueryQueueName      = "launchpad_rpc_query"
	LaunchpadRPCQueryWorkerPoolSize = 50

	TransactionCreateQueueName      = "transaction_create_queue"
	TransactionCreateWorkerPoolSize = 50
)

var (
	queueConsumer consumer.Consumer
	stopChan      = make(chan struct{})
)

func Consume() error {
	var err error
	prefix := setting.RabbitMQSetting.Prefix
	queueConsumer, err = consumer.NewConsumer(consumer.RabbitMQ)
	if err != nil {
		return err
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	walletCreateMsgChan, err := queueConsumer.FetchMessage(ctx, prefix+WalletCreateQueueName)
	if err != nil {
		return err
	}

	log.Infof("Start consuming messsages on queue %s", prefix+WalletCreateQueueName)
	for i := 0; i < WalletCreateWorkerPoolSize; i++ {
		go handlers.CreateWallet(ctx, i, walletCreateMsgChan)
	}

	sponsorWalletCreateMsgChan, err := queueConsumer.FetchMessage(ctx, prefix+SponsorWalletCreateQueueName)
	if err != nil {
		return err
	}

	log.Infof("Start consuming messsages on queue %s", prefix+SponsorWalletCreateQueueName)
	for i := 0; i < SponsorWalletCreateWorkerPoolSize; i++ {
		go handlers.CreateSponsorWallet(ctx, i, sponsorWalletCreateMsgChan)
	}

	sponsorWalletRPCQueryMsgChan, err := queueConsumer.FetchMessage(ctx, prefix+SponsorWalletRPCQueryQueueName)
	if err != nil {
		return err
	}

	log.Infof("Start consuming messsages on queue %s", prefix+SponsorWalletRPCQueryQueueName)
	for i := 0; i < SponsorWalletRPCQueryWorkerPoolSize; i++ {
		go handlers.HandleSponsorWalletRPCQuery(ctx, i, sponsorWalletRPCQueryMsgChan)
	}

	walletRPCQueryMsgChan, err := queueConsumer.FetchMessage(ctx, prefix+WalletRPCQueryQueueName)
	if err != nil {
		return err
	}

	log.Infof("Start consuming messsages on queue %s", prefix+WalletRPCQueryQueueName)
	for i := 0; i < WalletRPCQueryWorkerPoolSize; i++ {
		go handlers.HandleWalletRPCQuery(ctx, i, walletRPCQueryMsgChan)
	}

	txCreateMsgChan, err := queueConsumer.FetchMessage(ctx, prefix+TransactionCreateQueueName)
	if err != nil {
		return err
	}

	log.Infof("Start consuming messsages on queue %s", prefix+TransactionCreateQueueName)
	for i := 0; i < TransactionCreateWorkerPoolSize; i++ {
		go handlers.CreateTransaction(ctx, i, txCreateMsgChan)
	}

	lpRPCQueryMsgChan, err := queueConsumer.FetchMessage(ctx, prefix+LaunchpadRPCQueryQueueName)
	if err != nil {
		return err
	}

	log.Infof("Start consuming messsages on queue %s", prefix+LaunchpadRPCQueryQueueName)
	for i := 0; i < LaunchpadRPCQueryWorkerPoolSize; i++ {
		go handlers.HandleLaunchpadRPCQuery(ctx, i, lpRPCQueryMsgChan)
	}

	select {
	case <-stopChan:
		return nil
	}
}

// Stop method to gracefully stop the workers and close the consumer
func Stop() error {
	log.Infof("Stopping queue consumers...")
	stopChan <- struct{}{}
	if queueConsumer != nil {
		return queueConsumer.Close()
	}
	return nil
}
