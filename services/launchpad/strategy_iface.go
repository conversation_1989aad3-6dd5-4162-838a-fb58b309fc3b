package launchpad

import (
	"github.com/shopspring/decimal"
	"openedu-blockchain/models"
)

type ProcessLaunchpadStrategy interface {
	InitPool(account Account, req *InitPoolRequest) (*InitPoolResponse, error)
	ApprovePool(req *ApprovePoolRequest) (*ApprovePoolStatusResponse, error)
	UpdatePoolFundingTime(req *UpdatePoolFundingTimeRequest) (*UpdatePoolFundingTimeResponse, error)
	Pledge(account Account, req *PledgeLaunchpadRequest) (*PledgeLaunchpadResponse, error)
	CancelPool(account Account, req *CancelPoolRequest) (*CancelPoolResponse, error)
	GetVotingPowers(req *GetVotingPowersRequest) (*GetVotingPowersResponse, error)
	CheckFundingResult(req *CheckFundingRequest) (*CheckFundingResponse, error)
	ContinueWithPartialFund(req *ContinueLpPartialFundRequest) (*ContinueLpPartialFundResponse, error)
	SetPoolFundingTime(account Account, req *SetPoolFundingTimeRequest) (*SetPoolFundingTimeResponse, error)
	WithdrawToCreator(req *WithdrawToCreatorRequest) (*WithdrawToCreatorResponse, error)
	ClaimRefund(account Account, req *ClaimRefundRequest) (*ClaimRefundResponse, error)
	UpdatePoolStatus(req *UpdatePoolStatusRequest) (*UpdatePoolStatusStatusResponse, error)
}

type Account interface {
	GetAddress() string
	GetNetwork() models.BlockchainNetwork
	GetPublicKey() string
	GetPrivateKey() (string, error)
}

type InitPoolRequest struct {
	LaunchpadID      string                 `json:"launchpad_id"`
	Token            models.BlockchainToken `json:"token"`
	MinPledge        decimal.Decimal        `json:"min_pledge"`
	FundingStartDate int64                  `json:"funding_start_date"`
	FundingEndDate   int64                  `json:"funding_end_date"`
	TargetFunding    decimal.Decimal        `json:"target_funding"`
	IsMainnet        bool                   `json:"is_mainnet"`
}

type InitPoolResponse struct {
	ContractID string                   `json:"contract_id"`
	MethodName string                   `json:"method_name"`
	InputData  models.JSONB             `json:"input_data"`
	Deposit    decimal.Decimal          `json:"deposit"`
	Token      models.BlockchainToken   `json:"token"`
	BlockHash  string                   `json:"block_hash"`
	TxHash     string                   `json:"tx_hash"`
	GasLimit   uint64                   `json:"gas_limit"`
	GasBurnt   uint64                   `json:"gas_burnt"`
	Nonce      uint64                   `json:"nonce"`
	TxDetails  interface{}              `json:"tx_details"`
	Status     models.TransactionStatus `json:"status"`
	PoolID     string                   `json:"pool_id"`
	ErrorMsg   string                   `json:"error_msg"`
}

type ApprovePoolRequest struct {
	PoolID     string                   `json:"pool_id"`
	IsApproved bool                     `json:"is_approved"`
	Network    models.BlockchainNetwork `json:"network"`
	IsMainnet  bool                     `json:"is_mainnet"`
}

type ApprovePoolStatusResponse struct {
	ContractID string                   `json:"contract_id"`
	MethodName string                   `json:"method_name"`
	InputData  models.JSONB             `json:"input_data"`
	Deposit    decimal.Decimal          `json:"deposit"`
	Token      models.BlockchainToken   `json:"token"`
	BlockHash  string                   `json:"block_hash"`
	TxHash     string                   `json:"tx_hash"`
	GasLimit   uint64                   `json:"gas_limit"`
	GasBurnt   uint64                   `json:"gas_burnt"`
	Nonce      uint64                   `json:"nonce"`
	TxDetails  interface{}              `json:"tx_details"`
	Status     models.TransactionStatus `json:"status"`
	ErrorMsg   string                   `json:"error_msg"`
}

type UpdatePoolFundingTimeRequest struct {
	PoolID           string                   `json:"pool_id"`
	FundingStartDate int64                    `json:"funding_start_date"`
	FundingEndDate   int64                    `json:"funding_end_date"`
	Network          models.BlockchainNetwork `json:"network"`
	IsMainnet        bool                     `json:"is_mainnet"`
}

type UpdatePoolFundingTimeResponse struct {
	ContractID string                   `json:"contract_id"`
	MethodName string                   `json:"method_name"`
	InputData  models.JSONB             `json:"input_data"`
	Deposit    decimal.Decimal          `json:"deposit"`
	Token      models.BlockchainToken   `json:"token"`
	BlockHash  string                   `json:"block_hash"`
	TxHash     string                   `json:"tx_hash"`
	GasLimit   uint64                   `json:"gas_limit"`
	GasBurnt   uint64                   `json:"gas_burnt"`
	Nonce      uint64                   `json:"nonce"`
	TxDetails  interface{}              `json:"tx_details"`
	Status     models.TransactionStatus `json:"status"`
	ErrorMsg   string                   `json:"error_msg"`
}

type CancelPoolRequest struct {
	PoolID    string `json:"pool_id"`
	IsMainnet bool   `json:"is_mainnet"`
}

type CancelPoolResponse struct {
	ContractID string                   `json:"contract_id"`
	MethodName string                   `json:"method_name"`
	InputData  models.JSONB             `json:"input_data"`
	Deposit    decimal.Decimal          `json:"deposit"`
	Token      models.BlockchainToken   `json:"token"`
	BlockHash  string                   `json:"block_hash"`
	TxHash     string                   `json:"tx_hash"`
	GasLimit   uint64                   `json:"gas_limit"`
	GasBurnt   uint64                   `json:"gas_burnt"`
	Nonce      uint64                   `json:"nonce"`
	TxDetails  interface{}              `json:"tx_details"`
	Status     models.TransactionStatus `json:"status"`
	ErrorMsg   string                   `json:"error_msg"`
}

type CheckFundingRequest struct {
	PoolID           string                   `json:"pool_id"`
	IsWaitingFunding bool                     `json:"is_waiting_funding"`
	Network          models.BlockchainNetwork `json:"network"`
	IsMainnet        bool                     `json:"is_mainnet"`
}

type CheckFundingResponse struct {
	ContractID string                   `json:"contract_id"`
	MethodName string                   `json:"method_name"`
	InputData  models.JSONB             `json:"input_data"`
	Deposit    decimal.Decimal          `json:"deposit"`
	Token      models.BlockchainToken   `json:"token"`
	BlockHash  string                   `json:"block_hash"`
	TxHash     string                   `json:"tx_hash"`
	GasLimit   uint64                   `json:"gas_limit"`
	GasBurnt   uint64                   `json:"gas_burnt"`
	Nonce      uint64                   `json:"nonce"`
	TxDetails  interface{}              `json:"tx_details"`
	Status     models.TransactionStatus `json:"status"`
	PoolID     string                   `json:"pool_id"`
	PoolStatus string                   `json:"pool_status"`
	ErrorMsg   string                   `json:"error_msg"`
}

type ContinueLpPartialFundRequest struct {
	PoolID     string                   `json:"pool_id"`
	IsApproved bool                     `json:"is_approved"`
	Network    models.BlockchainNetwork `json:"network"`
	IsMainnet  bool                     `json:"is_mainnet"`
}

type ContinueLpPartialFundResponse struct {
	ContractID string                   `json:"contract_id"`
	MethodName string                   `json:"method_name"`
	InputData  models.JSONB             `json:"input_data"`
	Deposit    decimal.Decimal          `json:"deposit"`
	Token      models.BlockchainToken   `json:"token"`
	BlockHash  string                   `json:"block_hash"`
	TxHash     string                   `json:"tx_hash"`
	GasLimit   uint64                   `json:"gas_limit"`
	GasBurnt   uint64                   `json:"gas_burnt"`
	Nonce      uint64                   `json:"nonce"`
	TxDetails  interface{}              `json:"tx_details"`
	Status     models.TransactionStatus `json:"status"`
	PoolID     string                   `json:"pool_id"`
	PoolStatus string                   `json:"pool_status"`
	ErrorMsg   string                   `json:"error_msg"`
}

type PledgeLaunchpadRequest struct {
	PoolID    string                 `json:"pool_id"`
	Amount    decimal.Decimal        `json:"amount"`
	Token     models.BlockchainToken `json:"token"`
	IsMainnet bool                   `json:"is_mainnet"`
}

type ClaimRefundRequest struct {
	PoolID    string                 `json:"pool_id"`
	Token     models.BlockchainToken `json:"token"`
	IsMainnet bool                   `json:"is_mainnet"`
}

type ClaimRefundResponse struct {
	ContractID string                   `json:"contract_id"`
	MethodName string                   `json:"method_name"`
	InputData  models.JSONB             `json:"input_data"`
	Deposit    decimal.Decimal          `json:"deposit"`
	Token      models.BlockchainToken   `json:"token"`
	BlockHash  string                   `json:"block_hash"`
	TxHash     string                   `json:"tx_hash"`
	GasLimit   uint64                   `json:"gas_limit"`
	GasBurnt   uint64                   `json:"gas_burnt"`
	Nonce      uint64                   `json:"nonce"`
	TxDetails  interface{}              `json:"tx_details"`
	Status     models.TransactionStatus `json:"status"`
	ErrorMsg   string                   `json:"error_msg"`
}

type PledgeLaunchpadResponse struct {
	ContractID string                   `json:"contract_id"`
	MethodName string                   `json:"method_name"`
	InputData  models.JSONB             `json:"input_data"`
	Deposit    decimal.Decimal          `json:"deposit"`
	Token      models.BlockchainToken   `json:"token"`
	BlockHash  string                   `json:"block_hash"`
	TxHash     string                   `json:"tx_hash"`
	GasLimit   uint64                   `json:"gas_limit"`
	GasBurnt   uint64                   `json:"gas_burnt"`
	Nonce      uint64                   `json:"nonce"`
	TxDetails  interface{}              `json:"tx_details"`
	Status     models.TransactionStatus `json:"status"`
	ErrorMsg   string                   `json:"error_msg"`
}

type GetVotingPowersRequest struct {
	PoolID    string                   `json:"pool_id"`
	Network   models.BlockchainNetwork `json:"network"`
	IsMainnet bool                     `json:"is_mainnet"`
}

type VotingPowerEntry struct {
	Address     string          `json:"address"`
	Amount      decimal.Decimal `json:"amount"`
	TotalAmount decimal.Decimal `json:"total_amount"`
	VotingPower float64         `json:"voting_power"`
}

type GetVotingPowersResponse struct {
	VotingPowers []*VotingPowerEntry `json:"voting_powers"`
}

type GetPoolDetailsRequest struct {
	PoolID    string                   `json:"pool_id"`
	Network   models.BlockchainNetwork `json:"network"`
	IsMainnet bool                     `json:"is_mainnet"`
}

type GetPoolDetailsResponse struct {
	CampaignId        string          `json:"campaign_id"`
	CreatorId         string          `json:"creator_id"`
	MinMultiplePledge int64           `json:"min_multiple_pledge"`
	PoolId            int64           `json:"pool_id"`
	StakingAmount     decimal.Decimal `json:"staking_amount"`
	Status            string          `json:"status"`
	TargetFunding     int64           `json:"target_funding"`
	TimeEndPledge     int64           `json:"time_end_pledge"`
	TimeStartPledge   int64           `json:"time_start_pledge"`
	TokenId           string          `json:"token_id"`
	TotalBalance      int64           `json:"total_balance"`
}

type SetPoolFundingTimeRequest struct {
	PoolID              string `json:"pool_id"`
	FundingStartDate    int64  `json:"funding_start_date"`
	FundingDurationDays int    `json:"funding_duration_days"`
	IsMainnet           bool   `json:"is_mainnet"`
}

type SetPoolFundingTimeResponse struct {
	ContractID string                   `json:"contract_id"`
	MethodName string                   `json:"method_name"`
	InputData  models.JSONB             `json:"input_data"`
	Deposit    decimal.Decimal          `json:"deposit"`
	Token      models.BlockchainToken   `json:"token"`
	BlockHash  string                   `json:"block_hash"`
	TxHash     string                   `json:"tx_hash"`
	GasLimit   uint64                   `json:"gas_limit"`
	GasBurnt   uint64                   `json:"gas_burnt"`
	Nonce      uint64                   `json:"nonce"`
	TxDetails  interface{}              `json:"tx_details"`
	Status     models.TransactionStatus `json:"status"`
	ErrorMsg   string                   `json:"error_msg"`
}

type WithdrawToCreatorRequest struct {
	PoolID    string                   `json:"pool_id"`
	Token     models.BlockchainToken   `json:"token"`
	Amount    decimal.Decimal          `json:"amount"`
	Network   models.BlockchainNetwork `json:"network"`
	IsMainnet bool                     `json:"is_mainnet"`
}

type WithdrawToCreatorResponse struct {
	ContractID string                   `json:"contract_id"`
	MethodName string                   `json:"method_name"`
	InputData  models.JSONB             `json:"input_data"`
	Deposit    decimal.Decimal          `json:"deposit"`
	Token      models.BlockchainToken   `json:"token"`
	BlockHash  string                   `json:"block_hash"`
	TxHash     string                   `json:"tx_hash"`
	GasLimit   uint64                   `json:"gas_limit"`
	GasBurnt   uint64                   `json:"gas_burnt"`
	Nonce      uint64                   `json:"nonce"`
	TxDetails  interface{}              `json:"tx_details"`
	Status     models.TransactionStatus `json:"status"`
	ErrorMsg   string                   `json:"error_msg"`
}

type UpdatePoolStatusRequest struct {
	PoolID    string                   `json:"pool_id"`
	Status    string                   `json:"status"`
	Network   models.BlockchainNetwork `json:"network"`
	IsMainnet bool                     `json:"is_mainnet"`
}

type UpdatePoolStatusStatusResponse struct {
	ContractID string                   `json:"contract_id"`
	MethodName string                   `json:"method_name"`
	InputData  models.JSONB             `json:"input_data"`
	Deposit    decimal.Decimal          `json:"deposit"`
	Token      models.BlockchainToken   `json:"token"`
	BlockHash  string                   `json:"block_hash"`
	TxHash     string                   `json:"tx_hash"`
	GasLimit   uint64                   `json:"gas_limit"`
	GasBurnt   uint64                   `json:"gas_burnt"`
	Nonce      uint64                   `json:"nonce"`
	TxDetails  interface{}              `json:"tx_details"`
	Status     models.TransactionStatus `json:"status"`
	ErrorMsg   string                   `json:"error_msg"`
}
