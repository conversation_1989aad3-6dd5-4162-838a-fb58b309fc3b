package launchpad

import "errors"

var (
	ErrAccountNotExists                    = errors.New("account does not exist")
	ErrGetPrivateKeyFailed                 = errors.New("get private key failed")
	ErrParsePrivateKeyFailed               = errors.New("parse private key failed")
	ErrGetAccountStateFailed               = errors.New("failed to get account state")
	ErrGetTokenIDFailed                    = errors.New("failed to get token id")
	ErrGetTokenDecimalsFailed              = errors.New("failed to get token decimals")
	ErrGetTokenContractIDFailed            = errors.New("failed to get token contract id")
	ErrGetFtBalanceFailed                  = errors.New("get ft balance failed")
	ErrGetStorageBalanceFailed             = errors.New("get storage balance failed")
	ErrGetStorageBoundsFailed              = errors.New("get storage bounds failed")
	ErrInsufficientGasFee                  = errors.New("insufficient gas fee")
	ErrInsufficientBalance                 = errors.New("insufficient balance")
	ErrInsufficientBalanceToStorageDeposit = errors.New("insufficient balance to storage deposit")
	ErrStorageDepositFailed                = errors.New("storage deposit failed")
	ErrInvalidAmount                       = errors.New("invalid amount")
	ErrGetSponsorBalanceFailed             = errors.New("get sponsor balance failed")
	ErrDecodePrivateKeyFailed              = errors.New("decode private key failed")
	ErrInitPoolFailed                      = errors.New("deposit sponsor gas failed")
	ErrDecodeResultFailed                  = errors.New("decode result failed")
)
