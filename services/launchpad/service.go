package launchpad

import "openedu-blockchain/models"

type ProcessLaunchpadService struct {
	resolver *StrategyResolver
}

func NewLaunchpadService() *ProcessLaunchpadService {
	return &ProcessLaunchpadService{
		resolver: NewStrategyResolver(),
	}
}

func (s *ProcessLaunchpadService) RegisterStrategy(network models.BlockchainNetwork, strategy ProcessLaunchpadStrategy) error {
	return s.resolver.RegisterStrategy(network, strategy)
}

func (s *ProcessLaunchpadService) InitPool(account Account, req *InitPoolRequest) (*InitPoolResponse, error) {
	strategy, err := s.resolver.PickStrategy(account)
	if err != nil {
		return nil, err
	}
	return strategy.InitPool(account, req)
}

func (s *ProcessLaunchpadService) ApprovePool(req *ApprovePoolRequest) (*ApprovePoolStatusResponse, error) {
	strategy, err := s.resolver.PickStrategyByNetwork(req.Network)
	if err != nil {
		return nil, err
	}
	return strategy.ApprovePool(req)
}

func (s *ProcessLaunchpadService) UpdatePoolFundingTime(req *UpdatePoolFundingTimeRequest) (*UpdatePoolFundingTimeResponse, error) {
	strategy, err := s.resolver.PickStrategyByNetwork(req.Network)
	if err != nil {
		return nil, err
	}
	return strategy.UpdatePoolFundingTime(req)
}

func (s *ProcessLaunchpadService) Pledge(account Account, req *PledgeLaunchpadRequest) (*PledgeLaunchpadResponse, error) {
	strategy, err := s.resolver.PickStrategy(account)
	if err != nil {
		return nil, err
	}
	return strategy.Pledge(account, req)
}

func (s *ProcessLaunchpadService) CancelPool(account Account, req *CancelPoolRequest) (*CancelPoolResponse, error) {
	strategy, err := s.resolver.PickStrategy(account)
	if err != nil {
		return nil, err
	}
	return strategy.CancelPool(account, req)
}

func (s *ProcessLaunchpadService) GetVotingPowers(req *GetVotingPowersRequest) (*GetVotingPowersResponse, error) {
	strategy, err := s.resolver.PickStrategyByNetwork(req.Network)
	if err != nil {
		return nil, err
	}
	return strategy.GetVotingPowers(req)
}

func (s *ProcessLaunchpadService) CheckFundingResult(req *CheckFundingRequest) (*CheckFundingResponse, error) {
	strategy, err := s.resolver.PickStrategyByNetwork(req.Network)
	if err != nil {
		return nil, err
	}
	return strategy.CheckFundingResult(req)
}

func (s *ProcessLaunchpadService) ContinueWithPartialFund(req *ContinueLpPartialFundRequest) (*ContinueLpPartialFundResponse, error) {
	strategy, err := s.resolver.PickStrategyByNetwork(req.Network)
	if err != nil {
		return nil, err
	}
	return strategy.ContinueWithPartialFund(req)
}

func (s *ProcessLaunchpadService) SetFundingTime(account Account, req *SetPoolFundingTimeRequest) (*SetPoolFundingTimeResponse, error) {
	strategy, err := s.resolver.PickStrategy(account)
	if err != nil {
		return nil, err
	}
	return strategy.SetPoolFundingTime(account, req)
}

func (s *ProcessLaunchpadService) WithdrawToCreator(req *WithdrawToCreatorRequest) (*WithdrawToCreatorResponse, error) {
	strategy, err := s.resolver.PickStrategyByNetwork(req.Network)
	if err != nil {
		return nil, err
	}
	return strategy.WithdrawToCreator(req)
}

func (s *ProcessLaunchpadService) ClaimRefund(account Account, req *ClaimRefundRequest) (*ClaimRefundResponse, error) {
	strategy, err := s.resolver.PickStrategy(account)
	if err != nil {
		return nil, err
	}
	return strategy.ClaimRefund(account, req)
}

func (s *ProcessLaunchpadService) UpdatePoolStatus(req *UpdatePoolStatusRequest) (*UpdatePoolStatusStatusResponse, error) {
	strategy, err := s.resolver.PickStrategyByNetwork(req.Network)
	if err != nil {
		return nil, err
	}
	return strategy.UpdatePoolStatus(req)
}
