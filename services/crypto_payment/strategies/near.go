package payment

import (
	"crypto/ed25519"
	"fmt"
	"github.com/aurora-is-near/near-api-go"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"math/big"
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/openedu_near_sdk"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/pkg/util"
	"openedu-blockchain/services/crypto_payment"
	"openedu-blockchain/services/crypto_transfer"
	"openedu-blockchain/services/nft"
	"strings"
	"sync"
	"time"
)

type NearPaymentService struct {
}

func (s *NearPaymentService) Payment(
	account crypto_payment.Account,
	req *crypto_payment.PaymentRequest,
) (*crypto_payment.Response, error) {

	nodeURLs := s.getNodeURLs(req.IsMainnet)
	contractID, method := s.getContractIDAndMethodForPayment(req)
	result := &crypto_payment.Response{
		ContractID: contractID,
		MethodName: method,
		GasLimit:   openedu_near_sdk.DefaultGasUint64,
	}

	// Check withdraw amount greater than zero
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return result, fmt.Errorf("%w: amount must be greater than 0: amount(%s)", nft.ErrInvalidAmount, req.Amount.String())
	}

	// Get private key from account
	privateKey, err := s.getPrivateKey(account)
	if err != nil {
		return nil, err
	}

	// Get token address
	tokenID, err := s.getTokenID(req.Token, req.IsMainnet)
	if err != nil {
		return result, fmt.Errorf("%w: %w", crypto_payment.ErrGetTokenContractIDFailed, err)
	}

	// Get token decimals
	numDecimals, err := s.getTokenDecimals(nodeURLs, account.GetAddress(), privateKey, tokenID)
	if err != nil {
		return result, fmt.Errorf("%w: %w", crypto_payment.ErrGetTokenDecimalsFailed, err)
	}

	// Build args
	result.InputData = s.buildArgsForPayment(req, numDecimals)

	// Get account state, ft balance and storage balance
	var wg sync.WaitGroup
	var accountState *openedu_near_sdk.AccountState
	var aErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		accountState, aErr = s.getAccountState(nodeURLs, account)
	}()

	var ftBalance decimal.Decimal
	var fErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		ftBalance, fErr = openedu_near_sdk.GetFtBalanceOfWithRetry(nodeURLs, account.GetAddress(), privateKey, tokenID)
	}()

	var storageBalance *openedu_near_sdk.StorageBalance
	var sErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		storageBalance, sErr = openedu_near_sdk.GetStorageBalanceOfWithRetry(nodeURLs, account.GetAddress(), privateKey, tokenID)
	}()

	wg.Wait()

	if aErr != nil {
		return result, aErr
	}

	if fErr != nil {
		return result, fmt.Errorf("%w: %w", crypto_payment.ErrGetFtBalanceFailed, fErr)
	}

	if sErr != nil {
		return result, fmt.Errorf("%w: %w", crypto_payment.ErrGetStorageBalanceFailed, sErr)
	}

	// Check sender account is already enough gas fee
	if accountState.Amount.LessThan(openedu_near_sdk.DefaultGasFeeInYocto) {
		err = fmt.Errorf("%w: insufficient gas fee: balance(%s) gas(%s)",
			crypto_transfer.ErrInsufficientGasFee, accountState.Amount.String(), openedu_near_sdk.DefaultGasFeeInYocto)

		return result, fmt.Errorf("%w: %w", crypto_payment.ErrInsufficientGasFee, err)
	}

	// Check sender account is already enough balance
	rawPurchaseAmount := util.ParseReadableAmount2RawValue(req.Amount, numDecimals)
	if rawPurchaseAmount.GreaterThan(ftBalance) {
		return result, fmt.Errorf("%w: %w", crypto_payment.ErrInsufficientBalance, err)
	}

	// Check whether user deposit storage or not
	if storageBalance.Total.Equal(decimal.Zero) {
		storageBounds, bErr := openedu_near_sdk.GetStorageBalanceBoundsWithRetry(nodeURLs, account.GetAddress(), privateKey, tokenID)
		if bErr != nil {
			return result, fmt.Errorf("%w: %w", crypto_payment.ErrGetStorageBoundsFailed, bErr)
		}

		minAmount := storageBounds.Min.Add(openedu_near_sdk.DefaultGasFeeInYocto)
		if accountState.Amount.LessThan(minAmount) {
			err = fmt.Errorf("insufficient balance to storage deposit: balance(%s) amount(%s)", accountState.Amount, minAmount)
			return result, fmt.Errorf("%w: %w", crypto_payment.ErrInsufficientBalanceToStorageDeposit, err)
		}

		// Storage deposit
		depositStorageTxDetails, dsErr := openedu_near_sdk.StorageDepositWithRetry(nodeURLs, account.GetAddress(), privateKey, tokenID, contractID, storageBounds.Min, openedu_near_sdk.DefaultGasUint64)
		if dsErr != nil {
			return result, fmt.Errorf("%w: %w", crypto_payment.ErrStorageDepositFailed, dsErr)
		}

		time.Sleep(models.DefaultDelay)
		depositStorageTxDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(nodeURLs, depositStorageTxDetails.TransactionOutcome.Id, account.GetAddress(), near.TxExecutionStatus_Default)
		if err != nil {
			return result, fmt.Errorf("%w: %w", crypto_payment.ErrStorageDepositFailed, dsErr)
		}

		if depositStorageTxDetails.Status.Failure != nil {
			return result, fmt.Errorf("%w: %w", crypto_payment.ErrStorageDepositFailed, fmt.Errorf("transaction status failed"))
		}
	}

	txDetails, err := openedu_near_sdk.FunctionCallWithRetry(
		nodeURLs,
		account.GetAddress(),
		privateKey,
		tokenID,
		method,
		result.InputData,
		openedu_near_sdk.DefaultGasUint64,
		*(big.NewInt(1)),
	)
	if err != nil {
		return nil, err
	}

	// Check transaction status
	time.Sleep(openedu_near_sdk.CheckTxnStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		account.GetAddress(),
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return nil, fmt.Errorf("%w: check transaction status failed: %w", crypto_payment.ErrPaymentFailed, err)
	}

	result.ContractID = tokenID
	result.Status = models.TxStatusSuccess
	result.TxHash = txDetails.TransactionOutcome.Id
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}
	return result, nil

}

func (s *NearPaymentService) ClaimEarning(account crypto_payment.Account, req *crypto_payment.ClaimEarningRequest) (*crypto_payment.Response, error) {
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	contractID, claimMethodName, getUserInfoMethodName := s.getParamsForClaimEarning(req)
	result := &crypto_payment.Response{
		ContractID: contractID,
		MethodName: claimMethodName,
		GasLimit:   openedu_near_sdk.DefaultGasUint64,
	}

	// Get private key from account
	privateKey, err := s.getPrivateKey(account)
	if err != nil {
		return nil, err
	}

	// Get token address
	tokenID, err := s.getTokenID(req.Token, req.IsMainnet)
	if err != nil {
		return result, fmt.Errorf("%w: %w", crypto_payment.ErrGetTokenContractIDFailed, err)
	}

	// Get account state, num decimals, user earning
	var wg sync.WaitGroup
	var accountState *openedu_near_sdk.AccountState
	var aErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		accountState, aErr = s.getAccountState(nodeURLs, account)
	}()

	var numDecimals uint8
	var dErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		numDecimals, dErr = s.getTokenDecimals(nodeURLs, account.GetAddress(), privateKey, tokenID)
	}()

	var userEarning *openedu_near_sdk.UserEarning
	var uErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		userEarning, uErr = openedu_near_sdk.GetUserEarningWithRetry(nodeURLs, privateKey, contractID, getUserInfoMethodName, account.GetAddress())
	}()

	wg.Wait()

	if aErr != nil {
		return result, aErr
	}

	if dErr != nil {
		return result, fmt.Errorf("%w: %w", crypto_payment.ErrGetTokenDecimalsFailed, dErr)
	}

	if uErr != nil {
		return result, fmt.Errorf("%w: %w", crypto_payment.ErrGetUserEarningFailed, uErr)
	}

	deposit, found := lo.Find(userEarning.Deposits, func(item *openedu_near_sdk.UserDeposit) bool {
		return item.TokenID == tokenID
	})
	if !found {
		err = fmt.Errorf("account %s does not have the earning record of token %s", account.GetAddress(), tokenID)
		return result, fmt.Errorf("%w: %w", crypto_payment.ErrInsufficientEarningToClaim, err)
	}

	// Check if earning amount is equal or less than 0
	if deposit.Amount.LessThanOrEqual(decimal.Zero) {
		err = fmt.Errorf("insufficient earning to claim: earning(%s)", deposit.Amount.String())
		return result, fmt.Errorf("%w: %w", crypto_payment.ErrInsufficientEarningToClaim, err)
	}
	result.Deposit = util.ParseRawValue2ReadableAmount(deposit.Amount, numDecimals)

	// Check sender account is already enough gas fee
	if accountState.Amount.LessThan(openedu_near_sdk.DefaultGasFeeInYocto) {
		return result, fmt.Errorf("%w: insufficient gas fee: balance(%s) gas(%s)",
			crypto_transfer.ErrInsufficientGasFee, accountState.Amount.String(), openedu_near_sdk.DefaultGasFeeInYocto)
	}

	txDetails, err := openedu_near_sdk.ClaimEarningWithRetry(
		nodeURLs,
		account.GetAddress(),
		privateKey,
		contractID,
		claimMethodName,
		tokenID,
		openedu_near_sdk.DefaultGasUint64,
		*big.NewInt(0),
	)
	if err != nil {
		return nil, err
	}

	// Check transaction status
	time.Sleep(openedu_near_sdk.CheckTxnStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		account.GetAddress(),
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return nil, fmt.Errorf("%w: check transaction status failed: %w", crypto_payment.ErrClaimEarningFailed, err)
	}

	result.ContractID = tokenID
	result.Status = models.TxStatusSuccess
	result.TxHash = txDetails.TransactionOutcome.Id
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}
	return result, nil
}

func (s *NearPaymentService) getNodeURLs(isMainnet bool) []string {
	return lo.If(isMainnet, setting.NearSetting.MainnetURLs).
		Else(setting.NearSetting.TestnetURLs)
}

func (s *NearPaymentService) getAccountState(nodeURLs []string, account nft.Account) (*openedu_near_sdk.AccountState, error) {
	state, err := openedu_near_sdk.GetAccountStateWithRetry(nodeURLs, account.GetAddress())
	if err == nil {
		return state, nil
	}
	if s.isAccountNotExistError(account, err) {
		return nil, fmt.Errorf("%w: %w", crypto_payment.ErrAccountNotExists, err)
	}
	return nil, fmt.Errorf("%w: %w", crypto_payment.ErrGetAccountStateFailed, err)
}

func (s *NearPaymentService) isAccountNotExistError(account nft.Account, err error) bool {
	return strings.Contains(err.Error(), fmt.Sprintf("account %s does not exist while viewing", account.GetAddress()))
}

func (s *NearPaymentService) getPrivateKey(account nft.Account) (ed25519.PrivateKey, error) {
	privateKeyStr, err := account.GetPrivateKey()
	if err != nil {
		return nil, fmt.Errorf("%w: %w", nft.ErrGetPrivateKeyFailed, err)
	}

	privateKey, err := util.Ed25519PrivateKeyFromString(util.Ed25519Prefix + privateKeyStr)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", nft.ErrParsePrivateKeyFailed, err)
	}

	return privateKey, nil
}

func (s *NearPaymentService) getContractIDAndMethodForPayment(req *crypto_payment.PaymentRequest) (string, string) {
	contractID := setting.NearSetting.TestnetPaymentContractID
	methodName := openedu_near_sdk.FtTransferCallMethodName
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetPaymentContractID
	}

	return contractID, methodName
}

func (s *NearPaymentService) buildArgsForPayment(
	req *crypto_payment.PaymentRequest,
	numDecimals uint8) models.JSONB {

	contractID := setting.NearSetting.TestnetPaymentContractID
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetPaymentContractID
	}

	args := models.JSONB{
		"receiver_id": contractID,
		"amount":      util.ParseReadableAmount2RawValue(req.Amount, numDecimals),
		"msg": "[" + strings.Join(lo.Map(req.ProfitDistributions, func(item *dto.ProfitDistribution, _ int) string {
			return fmt.Sprintf(`{"user_id": "%s", "amount": %s}`, item.Address, util.ParseReadableAmount2RawValue(item.Amount, numDecimals))
		}), ",") + "]",
	}
	return args
}

func (s *NearPaymentService) getParamsForClaimEarning(req *crypto_payment.ClaimEarningRequest) (string, string, string) {
	contractID := setting.NearSetting.TestnetPaymentContractID
	claimMethodName := setting.NearSetting.TestnetPaymentClaimMethod
	getUserInfoMethodName := setting.NearSetting.TestnetPaymentGetUserInfoMethod
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetPaymentContractID
		claimMethodName = setting.NearSetting.MainnetPaymentClaimMethod
		getUserInfoMethodName = setting.NearSetting.MainnetPaymentGetUserInfoMethod
	}

	return contractID, claimMethodName, getUserInfoMethodName
}

func (s *NearPaymentService) getTokenID(token models.BlockchainToken, isMainnet bool) (string, error) {
	var tokenID string
	switch token {
	case models.TokenUSDT:
		tokenID = lo.If(isMainnet, setting.NearSetting.MainnetUSDTContractID).Else(setting.NearSetting.TestnetUSDTContractID)

	case models.TokenUSDC:
		tokenID = lo.If(isMainnet, setting.NearSetting.MainnetUSDCContractID).Else(setting.NearSetting.TestnetUSDCContractID)
	}
	if tokenID == "" {
		return "", crypto_payment.ErrUnsupportedToken
	}
	return tokenID, nil
}

// TODO refactor as util in pkg/openedu_near_sdk
func (s *NearPaymentService) getTokenDecimals(
	nodeURLs []string,
	accountID string,
	privateKey ed25519.PrivateKey,
	tokenID string,
) (uint8, error) {
	switch tokenID {
	case setting.NearSetting.MainnetUSDTContractID:
		return uint8(setting.NearSetting.MainnetUSDTDecimals), nil

	case setting.NearSetting.MainnetUSDCContractID:
		return uint8(setting.NearSetting.MainnetUSDCDecimals), nil

	case setting.NearSetting.MainnetOpenEduContractID:
		return uint8(setting.NearSetting.MainnetOpenEduDecimals), nil

	case setting.NearSetting.TestnetUSDTContractID:
		return uint8(setting.NearSetting.TestnetUSDTDecimals), nil

	case setting.NearSetting.TestnetUSDCContractID:
		return uint8(setting.NearSetting.TestnetUSDCDecimals), nil

	case setting.NearSetting.TestnetOpenEduContractID:
		return uint8(setting.NearSetting.TestnetOpenEduDecimals), nil
	}

	// TODO implement cache
	ftMetadata, err := openedu_near_sdk.GetFtMetadataWithRetry(nodeURLs, accountID, privateKey, tokenID)
	if err != nil {
		return 0, err
	}

	return ftMetadata.Decimals, nil
}
