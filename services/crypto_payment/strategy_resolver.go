package crypto_payment

import (
	"fmt"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/log"
	"sync"
)

type StrategyResolver struct {
	strategies map[models.BlockchainNetwork]PaymentStrategy
	mu         sync.RWMutex
}

func NewStrategyResolver() *StrategyResolver {
	return &StrategyResolver{
		strategies: make(map[models.BlockchainNetwork]PaymentStrategy),
	}
}

// RegisterStrategy registers a transfer strategy for a specific transfer type
func (r *StrategyResolver) RegisterStrategy(network models.BlockchainNetwork, strategy PaymentStrategy) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if network == "" {
		return fmt.Errorf("network cannot empty")
	}

	if strategy == nil {
		return fmt.Errorf("strategy cannot be nil")
	}

	if _, exists := r.strategies[network]; exists {
		log.Warnf("Overwriting existing strategy: %v", network)
	}

	r.strategies[network] = strategy
	log.Infof("Registered new mint NFT strategy for network: %v", network)
	return nil
}

// PickStrategy picks the appropriate minting strategy based on the account's network
func (r *StrategyResolver) PickStrategy(account Account) (PaymentStrategy, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	strategy, exists := r.strategies[account.GetNetwork()]
	if !exists {
		return nil, fmt.Errorf("failed to find minting strategy for network: %v", account.GetNetwork())
	}
	return strategy, nil
}
