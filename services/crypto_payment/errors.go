package crypto_payment

import "errors"

var (
	ErrConnectRPCFailed                    = errors.New("connect rpc failed")
	ErrGetPrivateKeyFailed                 = errors.New("get private key failed")
	ErrGetTokenContractIDFailed            = errors.New("failed to get token contract id")
	ErrGetTokenDecimalsFailed              = errors.New("failed to get token decimals")
	ErrUnsupportedToken                    = errors.New("unsupported token")
	ErrGetFtBalanceFailed                  = errors.New("get ft balance failed")
	ErrGetStorageBalanceFailed             = errors.New("get storage balance failed")
	ErrGetStorageBoundsFailed              = errors.New("get storage bounds failed")
	ErrAccountNotExists                    = errors.New("account does not exist")
	ErrGetAccountStateFailed               = errors.New("failed to get account state")
	ErrInsufficientBalanceToStorageDeposit = errors.New("insufficient balance to storage deposit")
	ErrInsufficientGasFee                  = errors.New("insufficient gas fee")
	ErrInsufficientBalance                 = errors.New("insufficient balance")
	ErrStorageDepositFailed                = errors.New("storage deposit failed")
	ErrGetUserEarningFailed                = errors.New("get user earning failed")
	ErrInsufficientEarningToClaim          = errors.New("insufficient earning to claim")
	ErrPaymentFailed                       = errors.New("payment failed")
	ErrClaimEarningFailed                  = errors.New("claim earning failed")
)
