package crypto_payment

import "openedu-blockchain/models"

type PaymentService struct {
	resolver *StrategyResolver
}

func NewPaymentService() *PaymentService {
	return &PaymentService{
		resolver: NewStrategyResolver(),
	}
}

func (s *PaymentService) RegisterStrategy(network models.BlockchainNetwork, strategy PaymentStrategy) error {
	return s.resolver.RegisterStrategy(network, strategy)
}

func (s *PaymentService) Payment(account Account, req *PaymentRequest) (*Response, error) {
	strategy, err := s.resolver.PickStrategy(account)
	if err != nil {
		return nil, err
	}
	return strategy.Payment(account, req)
}

func (s *PaymentService) ClaimEarning(account Account, req *ClaimEarningRequest) (*Response, error) {
	strategy, err := s.resolver.PickStrategy(account)
	if err != nil {
		return nil, err
	}
	return strategy.ClaimEarning(account, req)
}
