package services

import (
	"openedu-blockchain/models"
	"openedu-blockchain/services/crypto_payment"
	"openedu-blockchain/services/crypto_transfer"
	"openedu-blockchain/services/launchpad"
	"openedu-blockchain/services/nft"
)

type TransactionService struct {
	transferService  *crypto_transfer.TransferService
	nftService       *nft.MintNftService
	launchpadService *launchpad.ProcessLaunchpadService
	paymentService   *crypto_payment.PaymentService
}

type TransactionServiceDeps struct {
	TransferService  *crypto_transfer.TransferService
	NftService       *nft.MintNftService
	LaunchpadService *launchpad.ProcessLaunchpadService
	PaymentService   *crypto_payment.PaymentService
}

func NewTransactionService(deps *TransactionServiceDeps) *TransactionService {
	return &TransactionService{
		transferService:  deps.TransferService,
		nftService:       deps.NftService,
		launchpadService: deps.LaunchpadService,
		paymentService:   deps.PaymentService,
	}
}

func (s *TransactionService) newTxnResponse(code int, message string, txDetails any) models.JSONB {
	resp := models.JSONB{
		"code":    code,
		"message": message,
	}
	if txDetails != nil {
		resp["transaction"] = txDetails
	}
	return resp
}
