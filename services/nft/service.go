package nft

import (
	"fmt"
	"openedu-blockchain/models"
)

type MintNftService struct {
	resolver *StrategyResolver
}

func NewMintNftService() *MintNftService {
	return &MintNftService{
		resolver: NewStrategyResolver(),
	}
}

func (s *MintNftService) RegisterStrategy(network models.BlockchainNetwork, strategy MintNftStrategy) error {
	return s.resolver.RegisterStrategy(network, strategy)
}

func (s *MintNftService) GetStrategy(network models.BlockchainNetwork) (MintNftStrategy, error) {
	return s.resolver.PickStrategyByNetwork(network)
}

func (s *MintNftService) DepositSponsorGas(account Account, req *DepositSponsorGasRequest) (*DepositSponsorGasResponse, error) {
	strategy, err := s.resolver.PickStrategy(account)
	if err != nil {
		return nil, err
	}
	return strategy.DepositSponsorGas(account, req)
}

func (s *MintNftService) WithdrawSponsorGas(account Account, req *WithdrawSponsorGasRequest) (*WithdrawSponsorGasResponse, error) {
	strategy, err := s.resolver.PickStrategy(account)
	if err != nil {
		return nil, err
	}
	return strategy.WithdrawSponsorGas(account, req)
}

func (s *MintNftService) MintNft(account Account, req *MintNftRequest) (*MintNftResponse, error) {
	var strategy MintNftStrategy
	var err error

	if req.Network != "" {
		strategy, err = s.resolver.PickStrategyByNetwork(req.Network)
	} else {
		strategy, err = s.resolver.PickStrategy(account)
	}

	if err != nil {
		return nil, err
	}

	return strategy.MintNFT(account, req)
}

func (s *MintNftService) CreatePermit(req *CreatePermitRequest) (*CreatePermitResponse, error) {
	strategy, err := s.resolver.PickStrategyByNetwork(req.Network)
	if err != nil {
		return nil, err
	}

	eip712Strategy, ok := strategy.(EIP712Strategy)
	if !ok {
		return nil, fmt.Errorf("network %s does not support EIP-712 permit", req.Network)
	}

	return eip712Strategy.CreatePermit(req)
}

func (s *MintNftService) MintNftWithPermit(account Account, req *MintNftWithPermitRequest) (*MintNftResponse, error) {
	var strategy MintNftStrategy
	var err error

	if req.Network != "" {
		strategy, err = s.resolver.PickStrategyByNetwork(req.Network)
	} else {
		strategy, err = s.resolver.PickStrategy(account)
	}

	if err != nil {
		return nil, err
	}

	eip712Strategy, ok := strategy.(EIP712Strategy)
	if !ok {
		return nil, fmt.Errorf("network %s does not support EIP-712 permit", req.Network)
	}

	return eip712Strategy.MintNFTWithPermit(account, req)
}
