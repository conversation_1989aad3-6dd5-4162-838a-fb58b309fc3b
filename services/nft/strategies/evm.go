package strategies

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math/big"
	"math/rand"
	"net/http"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/openedu_eth_sdk"
	"openedu-blockchain/pkg/openedu_eth_sdk/bindings"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/pkg/util"
	"openedu-blockchain/services/nft"
	"openedu-blockchain/services/nft/metadata"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/shopspring/decimal"
)

const (
	MethodDepositSponsorGas  = "depositSponsorGas"
	MethodWithdrawSponsorGas = "withdrawSponsorGas"
	MethodMint               = "mint"
	MethodMintWithPermit     = "mintWithPermit"

	GasPriceMultiplier = 300
	GasPriceDivisor    = 100

	PaymasterMintNFTEndpoint = "/api/v1/mint-nft"
)

type EVMMintNftService struct {
	metadataService metadata.Service
}

func NewEVMMintNftService() nft.MintNftStrategy {
	log.Infof("Initializing EVMMintNftService")
	metadataService := metadata.NewPinataService()
	log.Infof("Created metadata service: %+v", metadataService)

	service := &EVMMintNftService{
		metadataService: metadataService,
	}
	log.Infof("Initialized EVMMintNftService with metadata service")
	return service
}

type PaymasterMintNFTRequest struct {
	ReceiverAddress string `json:"receiver_address"`
	TokenURI        string `json:"token_uri"`
	IsMain          bool   `json:"is_main"`
}

type PaymasterMintNFTResponseData struct {
	Status      string `json:"status"`
	TxHash      string `json:"tx_hash"`
	UserOpHash  string `json:"user_op_hash"`
	BlockNumber string `json:"block_number"`
	BlockHash   string `json:"block_hash"`
	Timestamp   int64  `json:"timestamp"`
	Network     string `json:"network"`
}

type PaymasterMintNFTResponse struct {
	Code    int                          `json:"code"`
	Message string                       `json:"message"`
	Data    PaymasterMintNFTResponseData `json:"data"`
}

func (s *EVMMintNftService) DepositSponsorGas(
	account nft.Account,
	req *nft.DepositSponsorGasRequest,
) (*nft.DepositSponsorGasResponse, error) {
	log.Infof("Starting deposit sponsor gas for course: %s, amount: %s", req.CourseCuid, req.Amount.String())

	contractAddress, err := s.getNFTContractAddress(req.IsMainnet)
	if err != nil {
		log.Errorf("Failed to get NFT contract address: %v", err)
		return nil, fmt.Errorf("%w: %v", nft.ErrContractNotConfigured, err)
	}

	result := &nft.DepositSponsorGasResponse{
		ContractID: contractAddress.Hex(),
		MethodName: MethodDepositSponsorGas,
		InputData: models.JSONB{
			"course_cuid": req.CourseCuid,
			"amount":      req.Amount.String(),
			"sponsor":     account.GetAddress(),
		},
		GasLimit: openedu_eth_sdk.DefaultGasLimit,
		Status:   models.TxStatusPending,
	}

	if req.Amount.LessThanOrEqual(decimal.Zero) {
		log.Errorf("Invalid amount: %s", req.Amount.String())
		return result, fmt.Errorf("%w: amount must be greater than 0: amount(%s)", nft.ErrInvalidAmount, req.Amount.String())
	}

	client, err := s.getEthClient(req.IsMainnet)
	if err != nil {
		log.Errorf("Failed to get ETH client: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetEthClientFailed, err)
	}

	var err2 error
	contractAddress, err2 = s.getNFTContractAddress(req.IsMainnet)
	if err2 != nil {
		log.Errorf("Failed to get NFT contract address: %v", err2)
		return result, fmt.Errorf("%w: %v", nft.ErrContractNotConfigured, err2)
	}
	result.ContractID = contractAddress.Hex()

	privateKeyStr, err := account.GetPrivateKey()
	if err != nil {
		log.Errorf("Failed to get private key: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetPrivateKeyFailed, err)
	}

	privateKeyStr = util.RemoveHexPrefix(privateKeyStr)

	// Convert private key to ECDSA
	privateKey, err := crypto.HexToECDSA(privateKeyStr)
	if err != nil {
		log.Errorf("Failed to convert private key to ECDSA: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrParsePrivateKeyFailed, err)
	}

	// Get chain ID
	chainID, err := client.ChainID(context.Background())
	if err != nil {
		log.Errorf("Failed to get chain ID: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetEthClientFailed, err)
	}

	// Create transaction signer
	auth, err := bind.NewKeyedTransactorWithChainID(privateKey, chainID)
	if err != nil {
		log.Errorf("Failed to create transaction signer: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrDepositSponsorGasFailed, err)
	}

	// Get current nonce
	senderAddress := crypto.PubkeyToAddress(privateKey.PublicKey)
	nonce, err := client.PendingNonceAt(context.Background(), senderAddress)
	if err != nil {
		log.Errorf("Failed to get nonce: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetEthClientFailed, err)
	}
	auth.Nonce = big.NewInt(int64(nonce))

	// Set gas limit
	auth.GasLimit = uint64(openedu_eth_sdk.DefaultGasLimit)

	// Get gas price
	gasPrice, err := s.GetGasPrice(client)
	if err != nil {
		return result, err
	}
	auth.GasPrice = gasPrice

	amountInWei := req.Amount.BigInt()
	auth.Value = amountInWei

	network := account.GetNetwork()

	wallet, ok := account.(*models.Wallet)
	if !ok {
		log.Errorf("Account is not a wallet object")
		return result, fmt.Errorf("%w: invalid account type", nft.ErrDepositSponsorGasFailed)
	}

	sponsorWallet, err := models.Repository.SponsorWallet.FindOne(&models.SponsorWalletQuery{
		SponsorID: &wallet.UserID,
		Network:   &network,
	}, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			log.Errorf("Personal sponsor wallet not found for user: %s", wallet.UserID)
			return result, fmt.Errorf("%w: sponsor wallet not found", nft.ErrDepositSponsorGasFailed)
		}
		log.Errorf("Failed to find sponsor wallet: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrDepositSponsorGasFailed, err)
	}

	// Get sponsor wallet address as destination
	sponsorWalletAddress := common.HexToAddress(sponsorWallet.Address)

	log.Infof("Sending ETH to sponsor wallet with amount: %s ETH", req.Amount.String())

	tx := types.NewTransaction(
		uint64(auth.Nonce.Int64()),
		sponsorWalletAddress,
		amountInWei,
		auth.GasLimit,
		auth.GasPrice,
		nil,
	)

	signedTx, err := auth.Signer(auth.From, tx)
	if err != nil {
		log.Errorf("Failed to sign transaction: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrDepositSponsorGasFailed, err)
	}

	err = client.SendTransaction(context.Background(), signedTx)
	if err != nil {
		log.Errorf("Failed to send transaction: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrDepositSponsorGasFailed, err)
	}

	tx = signedTx

	result.TxHash = tx.Hash().Hex()
	result.Nonce = uint64(tx.Nonce())
	result.GasLimit = uint64(tx.Gas())
	log.Infof("Transaction sent successfully with hash: %s", result.TxHash)

	result.Status = models.TxStatusPending
	result.ErrorMsg = "Transaction sent successfully"

	receipt, err := s.waitForTransaction(client, tx.Hash())
	if err != nil {
		if err == context.DeadlineExceeded {
			log.Warnf("Timeout waiting for transaction confirmation, but transaction was sent successfully: %v", err)
			result.Status = models.TxStatusPending
			result.ErrorMsg = "Transaction sent successfully but confirmation timed out"
			return result, nil
		} else {
			log.Errorf("Error waiting for transaction: %v", err)
			result.Status = models.TxStatusFailed
			result.ErrorMsg = fmt.Sprintf("Error waiting for transaction: %v", err)
			return result, nil
		}
	}

	log.Infof("Transaction confirmed in block %s", receipt.BlockHash.Hex())

	result.BlockHash = receipt.BlockHash.Hex()
	result.GasBurnt = receipt.GasUsed

	gasCost := decimal.NewFromBigInt(gasPrice, 0).Mul(decimal.NewFromInt(int64(receipt.GasUsed)))
	gasCostEth := gasCost.Div(decimal.NewFromInt(1e18))

	result.InputData["gas_cost"] = gasCostEth.String()
	result.InputData["total_gas_cost"] = gasCostEth.String()

	if receipt.Status == 1 {
		log.Infof("Transaction %s completed successfully", tx.Hash().Hex())
		result.Status = models.TxStatusSuccess

		// Update sponsor wallet balance in db
		sponsorWallet.Balance = sponsorWallet.Balance.Add(req.Amount)
		if err := models.Repository.SponsorWallet.Update(sponsorWallet, nil); err != nil {
			log.Errorf("Failed to update sponsor wallet balance: %v", err)

		}
	} else {
		log.Warnf("Transaction %s reverted on blockchain", tx.Hash().Hex())
		result.Status = models.TxStatusFailed
		result.ErrorMsg = "Transaction reverted on blockchain"
	}

	log.Infof("Deposit sponsor gas completed successfully for transaction %s with status %s", result.TxHash, result.Status)
	return result, nil
}

func (s *EVMMintNftService) WithdrawSponsorGas(
	account nft.Account,
	req *nft.WithdrawSponsorGasRequest,
) (*nft.WithdrawSponsorGasResponse, error) {
	result := &nft.WithdrawSponsorGasResponse{
		ContractID: "",
		MethodName: MethodWithdrawSponsorGas,
		InputData: models.JSONB{
			"course_cuid": req.CourseCuid,
			"amount":      req.Amount.String(),
			"sponsor":     account.GetAddress(),
		},
		GasLimit: openedu_eth_sdk.DefaultGasLimit,
		Status:   models.TxStatusPending,
	}

	if req.Amount.LessThanOrEqual(decimal.Zero) {
		log.Errorf("Invalid amount: %s", req.Amount.String())
		return result, fmt.Errorf("%w: amount must be greater than 0: amount(%s)", nft.ErrInvalidAmount, req.Amount.String())
	}

	client, err := s.getEthClient(req.IsMainnet)
	if err != nil {
		log.Errorf("Failed to get ETH client: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetEthClientFailed, err)
	}
	defer client.Close()

	// Get sponsor wallet from db - find by user ID (personal sponsor wallet)
	network := account.GetNetwork()

	// Cast account to wallet to access UserID
	wallet, ok := account.(*models.Wallet)
	if !ok {
		log.Errorf("Account is not a wallet object")
		return result, fmt.Errorf("%w: invalid account type", nft.ErrWithdrawSponsorGasFailed)
	}

	sponsorWallet, err := models.Repository.SponsorWallet.FindOne(&models.SponsorWalletQuery{
		SponsorID: &wallet.UserID,
		Network:   &network,
	}, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			log.Errorf("Personal sponsor wallet not found for user: %s", wallet.UserID)
			return result, fmt.Errorf("%w: sponsor wallet not found", nft.ErrWithdrawSponsorGasFailed)
		}
		log.Errorf("Failed to find sponsor wallet: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrWithdrawSponsorGasFailed, err)
	}

	// Get sponsor wallet pk
	privateKeyHex, err := sponsorWallet.GetPrivateKey()
	if err != nil {
		log.Errorf("Failed to decrypt sponsor wallet private key: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrWithdrawSponsorGasFailed, err)
	}

	// Parse pk
	sponsorPrivateKey, err := crypto.HexToECDSA(privateKeyHex)
	if err != nil {
		log.Errorf("Failed to parse sponsor wallet private key: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrWithdrawSponsorGasFailed, err)
	}

	// Get sponsor wallet address
	sponsorAddress := common.HexToAddress(sponsorWallet.Address)

	// Check sponsor wallet balance
	balance, err := client.BalanceAt(context.Background(), sponsorAddress, nil)
	if err != nil {
		log.Errorf("Failed to get sponsor wallet balance: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrWithdrawSponsorGasFailed, err)
	}

	amountInWei := req.Amount.BigInt()
	if balance.Cmp(amountInWei) < 0 {
		log.Errorf("Insufficient sponsor wallet balance: balance(%s) amount(%s)", balance.String(), amountInWei.String())
		return result, fmt.Errorf("%w: insufficient sponsor wallet balance", nft.ErrInsufficientBalance)
	}

	// Get recipient address (user's wallet address)
	recipientAddress := common.HexToAddress(account.GetAddress())

	// Create transaction to transfer ETH from sponsor wallet to recipient
	nonce, err := client.PendingNonceAt(context.Background(), sponsorAddress)
	if err != nil {
		log.Errorf("Failed to get nonce: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrWithdrawSponsorGasFailed, err)
	}

	// Get gas price
	gasPrice, err := s.GetGasPrice(client)
	if err != nil {
		return result, err
	}

	// Create transaction
	tx := types.NewTransaction(
		nonce,
		recipientAddress,
		amountInWei,
		uint64(openedu_eth_sdk.DefaultGasLimit),
		gasPrice,
		nil,
	)

	// Get chain ID
	chainID, err := client.NetworkID(context.Background())
	if err != nil {
		log.Errorf("Failed to get chain ID: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrWithdrawSponsorGasFailed, err)
	}

	// Sign transaction with sponsor wallet pk
	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), sponsorPrivateKey)
	if err != nil {
		log.Errorf("Failed to sign transaction: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrWithdrawSponsorGasFailed, err)
	}

	// Send transaction
	err = client.SendTransaction(context.Background(), signedTx)
	if err != nil {
		log.Errorf("Failed to send transaction: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrWithdrawSponsorGasFailed, err)
	}

	result.TxHash = signedTx.Hash().Hex()
	result.Status = models.TxStatusSuccess

	// Update sponsor wallet balance in db
	sponsorWallet.Balance = sponsorWallet.Balance.Sub(req.Amount)
	if err := models.Repository.SponsorWallet.Update(sponsorWallet, nil); err != nil {
		log.Errorf("Failed to update sponsor wallet balance: %v", err)
	}

	log.Infof("Withdraw sponsor gas completed successfully for transaction %s with status %s", result.TxHash, result.Status)
	return result, nil
}

func (s *EVMMintNftService) uploadMetadataToIPFS(req *nft.MintNftRequest) (string, error) {
	log.Infof("Starting uploadMetadataToIPFS with request: %+v", req)
	if s.metadataService == nil {
		log.Errorf("Metadata service is not initialized")
		return "", fmt.Errorf("%w: metadata service is not initialized", nft.ErrMintNFTFailed)
	}
	if req.TokenMetadata.Title != "" && req.TokenMetadata.Description != "" {
		log.Infof("Creating metadata from core with title=%s, description=%s, mediaURL=%s, courseCuid=%s, courseOwnerAddress=%s, receiverWalletID=%s, network=%s",
			req.TokenMetadata.Title,
			req.TokenMetadata.Description,
			req.TokenMetadata.MediaURL,
			req.CourseCuid,
			req.CourseOwnerAddress,
			req.CourseOwnerAddress,
			string(req.Network))

		nftMetadata := s.metadataService.CreateMetadataFromCore(
			req.TokenMetadata.Title,
			req.TokenMetadata.Description,
			req.TokenMetadata.MediaURL,
			req.CourseCuid,
			req.CourseOwnerAddress,
			req.CourseOwnerAddress,
			string(req.Network),
		)

		log.Infof("Created metadata: %+v", nftMetadata)

		hash, err := s.metadataService.UploadMetadata(nftMetadata)
		if err != nil {
			log.Errorf("Failed to upload metadata to IPFS: %v", err)
			return "", fmt.Errorf("%w: %v", nft.ErrMintNFTFailed, err)
		}

		tokenURI := s.metadataService.GetTokenURI(hash)
		log.Infof("Metadata uploaded to IPFS with hash: %s, token URI: %s", hash, tokenURI)
		return tokenURI, nil
	} else if req.TokenMetadata.MediaURL != "" {
		log.Infof("Using media URL directly: %s", req.TokenMetadata.MediaURL)
		return req.TokenMetadata.MediaURL, nil
	}
	log.Infof("No metadata to upload")
	return "", nil
}

func (s *EVMMintNftService) MintNFT(account nft.Account, req *nft.MintNftRequest) (*nft.MintNftResponse, error) {
	log.Infof("MintNFT request: %+v", req)
	log.Infof("TokenMetadata: %+v", req.TokenMetadata)

	contractAddress, err := s.getNFTContractAddress(req.IsMainnet)
	if err != nil {
		log.Errorf("Failed to get NFT contract address: %v", err)
		return nil, fmt.Errorf("%w: %v", nft.ErrContractNotConfigured, err)
	}

	log.Infof("Starting NFT minting process for network: %s, contract: %s, receiver: %s, gas fee payer: %s",
		req.Network, contractAddress.Hex(), account.GetAddress(), req.GasFeePayer)

	result := &nft.MintNftResponse{
		MethodName: MethodMint,
		InputData: models.JSONB{
			"receiver":  account.GetAddress(),
			"token_uri": "",
		},
		Token:      models.TokenETH,
		GasLimit:   openedu_eth_sdk.DefaultGasLimit,
		Status:     models.TxStatusPending,
		ContractID: contractAddress.Hex(),
	}

	client, err := s.getEthClient(req.IsMainnet)
	if err != nil {
		log.Errorf("Failed to get ETH client: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetEthClientFailed, err)
	}

	nftContract, err := bindings.NewNFTsCertificate(contractAddress, client)
	if err != nil {
		log.Errorf("Failed to create NFT contract instance: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetContractFailed, err)
	}

	var privateKeyStr string

	switch req.GasFeePayer {
	case models.Platform:
		// Platform pays gas fee - use platform's private key
		privateKeyStr, err = s.getPlatformPrivateKey(req.IsMainnet)
		if err != nil {
			log.Errorf("Failed to get platform private key: %v", err)
			return result, fmt.Errorf("%w: %v", nft.ErrGetPrivateKeyFailed, err)
		}
		log.Infof("Using platform private key for gas fee")
	case models.Learner:
		// Learner pays gas fee - use learner's private key
		privateKeyStr, err = account.GetPrivateKey()
		if err != nil {
			log.Errorf("Failed to get user private key: %v", err)
			return result, fmt.Errorf("%w: %v", nft.ErrGetPrivateKeyFailed, err)
		}
		log.Infof("Using learner private key for gas fee")
	case models.Creator:
		// Creator pays gas fee - not implemented yet (TODO)
		log.Errorf("Creator gas fee payment not implemented yet")
		return result, fmt.Errorf("creator gas fee payment not implemented yet")
	case models.Paymaster:
		// Paymaster pays gas fee - use Coinbase Paymaster API
		log.Infof("Using Coinbase Paymaster for gas fee")
		return s.mintNFTWithPaymaster(account, req, result)
	default:
		log.Errorf("Invalid gas fee payer: %s", req.GasFeePayer)
		return result, fmt.Errorf("%w: invalid gas fee payer: %s", nft.ErrInvalidGasFeePayer, req.GasFeePayer)
	}

	privateKeyStr = util.RemoveHexPrefix(privateKeyStr)

	privateKey, err := crypto.HexToECDSA(privateKeyStr)
	if err != nil {
		log.Errorf("Failed to convert private key to ECDSA: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrParsePrivateKeyFailed, err)
	}

	chainID, err := client.ChainID(context.Background())
	if err != nil {
		log.Errorf("Failed to get chain ID: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetEthClientFailed, err)
	}

	auth, err := bind.NewKeyedTransactorWithChainID(privateKey, chainID)
	if err != nil {
		log.Errorf("Failed to create transaction signer: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrMintNFTFailed, err)
	}

	senderAddress := crypto.PubkeyToAddress(privateKey.PublicKey)
	nonce, err := client.PendingNonceAt(context.Background(), senderAddress)
	if err != nil {
		log.Errorf("Failed to get nonce: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetEthClientFailed, err)
	}
	auth.Nonce = big.NewInt(int64(nonce))

	auth.GasLimit = uint64(openedu_eth_sdk.DefaultGasLimit)

	gasPrice, err := s.GetGasPrice(client)
	if err != nil {
		return result, err
	}
	auth.GasPrice = gasPrice

	// Check if the account has enough balance for gas
	balance, err := client.BalanceAt(context.Background(), senderAddress, nil)
	if err != nil {
		log.Errorf("Failed to get account balance: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetEthClientFailed, err)
	}

	estimatedGasCost := new(big.Int).Mul(gasPrice, big.NewInt(int64(auth.GasLimit)))
	balanceEth := decimal.NewFromBigInt(balance, 0).Div(decimal.NewFromInt(1e18))
	costEth := decimal.NewFromBigInt(estimatedGasCost, 0).Div(decimal.NewFromInt(1e18))

	if balance.Cmp(estimatedGasCost) < 0 {
		log.Errorf("Insufficient funds for gas: balance %s ETH, estimated cost %s ETH",
			balanceEth.String(), costEth.String())
		return result, fmt.Errorf("%w: balance(%s ETH) < required(%s ETH)",
			nft.ErrInsufficientGasFee,
			balanceEth.String(),
			costEth.String())
	}

	log.Infof("Account has sufficient balance for gas: %s ETH > %s ETH", balanceEth.String(), costEth.String())
	tokenURI, err := s.uploadMetadataToIPFS(req)
	if err != nil {
		return result, err
	}

	receiverAddress := common.HexToAddress(account.GetAddress())

	result.InputData = models.JSONB{
		"receiver":  account.GetAddress(),
		"token_uri": tokenURI,
	}

	log.Infof("Sending mint transaction to contract %s for receiver %s with token URI %s", contractAddress.Hex(), receiverAddress.Hex(), tokenURI)
	tx, err := nftContract.Mint(auth, receiverAddress, tokenURI)
	if err != nil {
		log.Errorf("Failed to call mint function: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrMintNFTFailed, err)
	}

	result.TxHash = tx.Hash().Hex()
	result.Nonce = uint64(tx.Nonce())
	result.GasLimit = uint64(tx.Gas())
	log.Infof("Transaction sent successfully with hash: %s", result.TxHash)

	receipt, err := s.waitForTransaction(client, tx.Hash())
	if err != nil {
		if err == context.DeadlineExceeded {
			log.Warnf("Timeout waiting for transaction confirmation, but transaction was sent successfully: %v", err)
			result.Status = models.TxStatusPending
			result.ErrorMsg = "Transaction sent successfully but confirmation timed out"
			return result, nil
		} else {
			log.Errorf("Error waiting for transaction: %v", err)
			result.Status = models.TxStatusFailed
			result.ErrorMsg = fmt.Sprintf("Error waiting for transaction: %v", err)
			return result, nil
		}
	}
	log.Infof("Transaction confirmed in block %s", receipt.BlockHash.Hex())

	result.BlockHash = receipt.BlockHash.Hex()
	result.GasBurnt = receipt.GasUsed

	gasCost := decimal.NewFromBigInt(gasPrice, 0).Mul(decimal.NewFromInt(int64(receipt.GasUsed)))
	gasCostEth := gasCost.Div(decimal.NewFromInt(1e18))
	result.GasCost = gasCostEth
	result.TotalGasCost = gasCostEth

	if receipt.Status == 1 {
		result.Status = models.TxStatusSuccess
		log.Infof("Transaction successful")

		tokenID, err := s.extractTokenIDFromReceipt(receipt)
		if err == nil {
			result.InputData["token_id"] = tokenID.String()
			log.Infof("Minted NFT with token ID: %s", tokenID.String())
		} else {
			log.Warnf("Could not extract token ID from receipt: %v", err)
		}
	} else {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = "Transaction reverted"
		log.Warnf("Transaction reverted")
	}

	log.Infof("Mint NFT completed for transaction %s with status %s", result.TxHash, result.Status)
	return result, nil
}

func (s *EVMMintNftService) getEthClient(isMainnet bool) (*ethclient.Client, error) {
	var urls []string
	if isMainnet {
		urls = setting.EvmSetting.MainnetURLs
	} else {
		urls = setting.EvmSetting.TestnetURLs
	}

	if len(urls) == 0 {
		networkName := "mainnet"
		if !isMainnet {
			networkName = "testnet"
		}
		return nil, fmt.Errorf("no RPC URLs configured for %s", networkName)
	}

	return ethclient.Dial(urls[0])
}

func (s *EVMMintNftService) getNFTContractAddress(isMainnet bool) (common.Address, error) {
	var contractAddressStr string
	if isMainnet {
		contractAddressStr = setting.EvmSetting.MainnetNFTCertificateAddress
	} else {
		contractAddressStr = setting.EvmSetting.TestnetNFTCertificateAddress
	}

	if contractAddressStr == "" {
		return common.Address{}, nft.ErrContractNotConfigured
	}

	return common.HexToAddress(contractAddressStr), nil
}

func (s *EVMMintNftService) getPlatformPrivateKey(isMainnet bool) (string, error) {
	var privateKey string
	if isMainnet {
		privateKey = setting.EvmSetting.MainnetNFTOwnerPrivateKey
	} else {
		privateKey = setting.EvmSetting.TestnetNFTOwnerPrivateKey
	}

	if privateKey == "" {
		networkName := "mainnet"
		if !isMainnet {
			networkName = "testnet"
		}
		return "", fmt.Errorf("platform private key not configured for %s", networkName)
	}

	return privateKey, nil
}

func (s *EVMMintNftService) mintNFTWithPaymaster(account nft.Account, req *nft.MintNftRequest, result *nft.MintNftResponse) (*nft.MintNftResponse, error) {
	tokenURI, err := s.uploadMetadataToIPFS(req)
	if err != nil {
		return result, err
	}

	paymasterResp, err := s.callPaymasterAPI(account.GetAddress(), tokenURI, req.IsMainnet)
	if err != nil {
		log.Errorf("Failed to call Paymaster API: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrMintNFTFailed, err)
	}

	result.TxHash = paymasterResp.Data.TxHash
	result.Status = models.TxStatusSuccess
	result.BlockHash = paymasterResp.Data.BlockHash
	result.MethodName = MethodMintWithPermit

	result.InputData = models.JSONB{
		"receiver":  account.GetAddress(),
		"token_uri": tokenURI,
	}

	log.Infof("NFT minted successfully via Paymaster with tx hash: %s", result.TxHash)

	return result, nil
}

func (s *EVMMintNftService) callPaymasterAPI(receiverAddress string, tokenURI string, isMainnet bool) (*PaymasterMintNFTResponse, error) {
	if setting.PaymasterSetting.PaymasterURL == "" {
		return nil, fmt.Errorf("paymaster URL not configured")
	}

	if setting.PaymasterSetting.PaymasterAPIKey == "" {
		return nil, fmt.Errorf("paymaster API key not configured")
	}

	reqBody := PaymasterMintNFTRequest{
		ReceiverAddress: receiverAddress,
		TokenURI:        tokenURI,
		IsMain:          isMainnet,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	paymasterEndpoint := setting.PaymasterSetting.PaymasterURL + PaymasterMintNFTEndpoint
	req, err := http.NewRequest("POST", paymasterEndpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-API-Key", setting.PaymasterSetting.PaymasterAPIKey)

	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request to paymaster: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("paymaster API returned non-OK status: %d, body: %s", resp.StatusCode, string(body))
	}

	var paymasterResp PaymasterMintNFTResponse
	if err := json.Unmarshal(body, &paymasterResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if paymasterResp.Code != 200 || paymasterResp.Message != "Success" {
		return nil, fmt.Errorf("paymaster API returned error: code=%d, message=%s", paymasterResp.Code, paymasterResp.Message)
	}

	return &paymasterResp, nil
}

func (s *EVMMintNftService) waitForTransaction(client *ethclient.Client, txHash common.Hash) (*types.Receipt, error) {
	ctx, cancel := context.WithTimeout(context.Background(), openedu_eth_sdk.TxConfirmationTimeout)
	defer cancel()

	attempts := 0
	for {
		attempts++
		receipt, err := client.TransactionReceipt(ctx, txHash)
		if err == nil {
			return receipt, nil
		}

		if err != ethereum.NotFound {
			log.Errorf("Error checking transaction receipt: %v", err)
			return nil, err
		}

		select {
		case <-ctx.Done():
			log.Warnf("Timeout waiting for transaction to be mined: %v", ctx.Err())
			return nil, ctx.Err()
		case <-time.After(openedu_eth_sdk.TxPollingInterval):
		}
	}
}

func (s *EVMMintNftService) extractTokenIDFromReceipt(receipt *types.Receipt) (*big.Int, error) {
	transferEventSignature := common.HexToHash("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef")

	for _, eventLog := range receipt.Logs {
		if len(eventLog.Topics) == 4 && eventLog.Topics[0] == transferEventSignature {
			tokenID := new(big.Int).SetBytes(eventLog.Topics[3].Bytes())
			return tokenID, nil
		}
	}

	return nil, fmt.Errorf("token ID not found in transaction logs")
}

// Get gas
func (s *EVMMintNftService) GetGasPrice(client *ethclient.Client) (*big.Int, error) {
	gasPrice, err := client.SuggestGasPrice(context.Background())
	if err != nil {
		log.Errorf("Failed to get gas price: %v", err)
		return nil, fmt.Errorf("%w: %v", nft.ErrGetEthClientFailed, err)
	}

	gasPrice = new(big.Int).Mul(gasPrice, big.NewInt(GasPriceMultiplier))
	gasPrice = new(big.Int).Div(gasPrice, big.NewInt(GasPriceDivisor))

	return gasPrice, nil
}

func (s *EVMMintNftService) ExtractPermitSignatureComponents(signature []byte) (v uint8, r, sBytes [32]byte) {
	v = signature[64] + 27
	copy(r[:], signature[:32])
	copy(sBytes[:], signature[32:64])
	return
}

func (s *EVMMintNftService) CreatePermit(req *nft.CreatePermitRequest) (*nft.CreatePermitResponse, error) {
	contractAddress, err := s.getNFTContractAddress(req.IsMainnet)
	if err != nil {
		log.Errorf("Failed to get NFT contract address: %v", err)
		return nil, fmt.Errorf("%w: %v", nft.ErrContractNotConfigured, err)
	}

	client, err := s.getEthClient(req.IsMainnet)
	if err != nil {
		log.Errorf("Failed to get ETH client: %v", err)
		return nil, fmt.Errorf("%w: %v", nft.ErrGetEthClientFailed, err)
	}

	nftContract, err := bindings.NewNFTsCertificate(contractAddress, client)
	if err != nil {
		log.Errorf("Failed to create NFT contract instance: %v", err)
		return nil, fmt.Errorf("%w: %v", nft.ErrGetContractFailed, err)
	}

	privateKeyStr, err := s.getPlatformPrivateKey(req.IsMainnet)
	if err != nil {
		log.Errorf("Failed to get platform private key: %v", err)
		return nil, fmt.Errorf("%w: %v", nft.ErrGetPrivateKeyFailed, err)
	}

	privateKeyStr = util.RemoveHexPrefix(privateKeyStr)

	privateKey, err := crypto.HexToECDSA(privateKeyStr)
	if err != nil {
		log.Errorf("Failed to convert private key to ECDSA: %v", err)
		return nil, fmt.Errorf("%w: %v", nft.ErrParsePrivateKeyFailed, err)
	}
	tokenURI, err := s.uploadMetadataToIPFS(&nft.MintNftRequest{
		TokenMetadata:      req.TokenMetadata,
		CourseCuid:         req.CourseCuid,
		CourseOwnerAddress: req.ReceiverAddress,
		Network:            req.Network,
	})
	if err != nil {
		return nil, err
	}

	random := rand.New(rand.NewSource(time.Now().UnixNano()))
	nonce := random.Int63n(1000000000)

	deadline := time.Now().Add(5 * time.Minute).Unix()

	tokenID, err := nftContract.TokenCounter(nil)
	if err != nil {
		log.Errorf("Failed to get token counter from contract: %v", err)
		return nil, fmt.Errorf("%w: %v", nft.ErrGetContractFailed, err)
	}

	receiverAddress := common.HexToAddress(req.ReceiverAddress)
	messageHash, err := nftContract.GetMessageHash(
		nil,
		receiverAddress,
		tokenID,
		big.NewInt(nonce),
		big.NewInt(deadline),
	)
	if err != nil {
		log.Errorf("Failed to get message hash: %v", err)
		return nil, fmt.Errorf("%w: %v", nft.ErrGetMessageHashFailed, err)
	}

	domainInfo, err := nftContract.Eip712Domain(nil)
	if err != nil {
		log.Errorf("Failed to get EIP-712 domain: %v", err)
		return nil, fmt.Errorf("%w: %v", nft.ErrGetEIP712DomainFailed, err)
	}

	log.Infof("EIP-712 Domain: Name=%s, Version=%s, ChainID=%s, VerifyingContract=%s",
		domainInfo.Name, domainInfo.Version, domainInfo.ChainId.String(), domainInfo.VerifyingContract.Hex())

	signature, err := crypto.Sign(messageHash[:], privateKey)
	if err != nil {
		log.Errorf("Failed to sign message hash: %v", err)
		return nil, fmt.Errorf("%w: %v", nft.ErrSignMessageFailed, err)
	}

	v, r, sBytes := s.ExtractPermitSignatureComponents(signature)

	rHex := common.BytesToHash(r[:]).Hex()
	sHex := common.BytesToHash(sBytes[:]).Hex()

	log.Infof("Created permit signature for receiver %s with tokenID %s, nonce %d and deadline %d",
		req.ReceiverAddress, tokenID.String(), nonce, deadline)

	response := &nft.CreatePermitResponse{
		SignatureV:        uint8(v),
		SignatureR:        rHex,
		SignatureS:        sHex,
		SignatureNonce:    nonce,
		SignatureDeadline: deadline,
	}

	if tokenURI != "" {
		response.TokenURI = tokenURI
	}

	return response, nil
}

func (s *EVMMintNftService) MintNFTWithPermit(account nft.Account, req *nft.MintNftWithPermitRequest) (*nft.MintNftResponse, error) {
	contractAddress, err := s.getNFTContractAddress(req.IsMainnet)
	if err != nil {
		log.Errorf("Failed to get NFT contract address: %v", err)
		return nil, fmt.Errorf("%w: %v", nft.ErrContractNotConfigured, err)
	}

	log.Infof("Starting NFT minting with permit for network: %s, contract: %s, receiver: %s",
		req.Network, contractAddress.Hex(), req.CourseOwnerAddress)

	tokenURI, err := s.uploadMetadataToIPFS(&nft.MintNftRequest{
		TokenMetadata:      req.TokenMetadata,
		CourseCuid:         req.CourseCuid,
		CourseOwnerAddress: req.CourseOwnerAddress,
		Network:            req.Network,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to upload metadata: %w", err)
	}

	result := &nft.MintNftResponse{
		MethodName: MethodMintWithPermit,
		InputData: models.JSONB{
			"receiver":  req.CourseOwnerAddress,
			"token_uri": tokenURI,
			"nonce":     req.SignatureNonce,
			"deadline":  req.SignatureDeadline,
		},
		Token:      models.TokenETH,
		GasLimit:   openedu_eth_sdk.DefaultGasLimit,
		Status:     models.TxStatusPending,
		ContractID: contractAddress.Hex(),
	}

	client, err := s.getEthClient(req.IsMainnet)
	if err != nil {
		log.Errorf("Failed to get ETH client: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetEthClientFailed, err)
	}

	nftContract, err := bindings.NewNFTsCertificate(contractAddress, client)
	if err != nil {
		log.Errorf("Failed to create NFT contract instance: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetContractFailed, err)
	}

	privateKeyStr, err := account.GetPrivateKey()
	if err != nil {
		log.Errorf("Failed to get user private key: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetPrivateKeyFailed, err)
	}

	privateKeyStr = util.RemoveHexPrefix(privateKeyStr)

	privateKey, err := crypto.HexToECDSA(privateKeyStr)
	if err != nil {
		log.Errorf("Failed to convert private key to ECDSA: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrParsePrivateKeyFailed, err)
	}

	chainID, err := client.ChainID(context.Background())
	if err != nil {
		log.Errorf("Failed to get chain ID: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetEthClientFailed, err)
	}

	auth, err := bind.NewKeyedTransactorWithChainID(privateKey, chainID)
	if err != nil {
		log.Errorf("Failed to create transaction signer: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrMintNFTFailed, err)
	}

	senderAddress := crypto.PubkeyToAddress(privateKey.PublicKey)

	nonce, err := client.PendingNonceAt(context.Background(), senderAddress)
	if err != nil {
		log.Errorf("Failed to get nonce: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetEthClientFailed, err)
	}
	auth.Nonce = big.NewInt(int64(nonce))

	auth.GasLimit = uint64(openedu_eth_sdk.DefaultGasLimit)

	gasPrice, err := s.GetGasPrice(client)
	if err != nil {
		return result, err
	}
	auth.GasPrice = gasPrice

	balance, err := client.BalanceAt(context.Background(), senderAddress, nil)
	if err != nil {
		log.Errorf("Failed to get account balance: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrGetEthClientFailed, err)
	}

	estimatedGasCost := new(big.Int).Mul(gasPrice, big.NewInt(int64(auth.GasLimit)))
	balanceEth := decimal.NewFromBigInt(balance, 0).Div(decimal.NewFromInt(1e18))
	costEth := decimal.NewFromBigInt(estimatedGasCost, 0).Div(decimal.NewFromInt(1e18))

	if balance.Cmp(estimatedGasCost) < 0 {
		log.Errorf("Insufficient funds for gas: balance %s ETH, estimated cost %s ETH",
			balanceEth.String(), costEth.String())
		return result, fmt.Errorf("%w: balance(%s ETH) < required(%s ETH)",
			nft.ErrInsufficientGasFee,
			balanceEth.String(),
			costEth.String())
	}

	log.Infof("Account has sufficient balance for gas: %s ETH > %s ETH", balanceEth.String(), costEth.String())

	receiverAddress := common.HexToAddress(req.CourseOwnerAddress)
	signatureNonce := big.NewInt(req.SignatureNonce)
	signatureDeadline := big.NewInt(req.SignatureDeadline)
	signatureV := uint8(req.SignatureV)

	var signatureR, signatureS [32]byte
	rBytes := common.HexToHash(req.SignatureR).Bytes()
	sBytes := common.HexToHash(req.SignatureS).Bytes()
	copy(signatureR[:], rBytes[:32])
	copy(signatureS[:], sBytes[:32])

	if time.Now().Unix() > req.SignatureDeadline {
		log.Errorf("Signature expired: deadline %d < current time %d",
			req.SignatureDeadline, time.Now().Unix())
		return result, fmt.Errorf("%w: deadline %d < current time %d",
			nft.ErrSignatureExpired, req.SignatureDeadline, time.Now().Unix())
	}

	log.Infof("Calling mintWithPermit with receiver=%s, nonce=%d, deadline=%d",
		receiverAddress.Hex(), req.SignatureNonce, req.SignatureDeadline)

	tx, err := nftContract.MintWithPermit(
		auth,
		receiverAddress,
		tokenURI,
		signatureNonce,
		signatureDeadline,
		signatureV,
		signatureR,
		signatureS,
	)
	if err != nil {
		log.Errorf("Failed to call mintWithPermit: %v", err)
		return result, fmt.Errorf("%w: %v", nft.ErrMintNFTFailed, err)
	}

	result.TxHash = tx.Hash().Hex()
	result.Nonce = uint64(tx.Nonce())
	result.GasLimit = uint64(tx.Gas())
	log.Infof("Transaction sent successfully with hash: %s", result.TxHash)

	receipt, err := s.waitForTransaction(client, tx.Hash())
	if err != nil {
		if err == context.DeadlineExceeded {
			log.Warnf("Timeout waiting for transaction confirmation, but transaction was sent successfully: %v", err)
			result.Status = models.TxStatusPending
			result.ErrorMsg = "Transaction sent successfully but confirmation timed out"
			return result, nil
		} else {
			log.Errorf("Error waiting for transaction: %v", err)
			result.Status = models.TxStatusFailed
			result.ErrorMsg = fmt.Sprintf("Error waiting for transaction: %v", err)
			return result, nil
		}
	}

	log.Infof("Transaction confirmed in block %s", receipt.BlockHash.Hex())

	result.BlockHash = receipt.BlockHash.Hex()
	result.GasBurnt = receipt.GasUsed

	gasCost := decimal.NewFromBigInt(gasPrice, 0).Mul(decimal.NewFromInt(int64(receipt.GasUsed)))
	gasCostEth := gasCost.Div(decimal.NewFromInt(1e18))
	result.GasCost = gasCostEth
	result.TotalGasCost = gasCostEth

	if receipt.Status == 1 {
		result.Status = models.TxStatusSuccess
		log.Infof("Transaction successful")

		tokenID, err := s.extractTokenIDFromReceipt(receipt)
		if err == nil {
			result.InputData["token_id"] = tokenID.String()
			log.Infof("Minted NFT with token ID: %s", tokenID.String())
		} else {
			log.Warnf("Could not extract token ID from receipt: %v", err)
		}
	} else {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = "Transaction reverted"
		log.Warnf("Transaction reverted")
	}

	log.Infof("Mint NFT with permit completed for transaction %s with status %s", result.TxHash, result.Status)
	return result, nil
}
