package strategies

import (
	"crypto/ed25519"
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/aurora-is-near/near-api-go"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"golang.org/x/crypto/nacl/sign"
	"math/big"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/openedu_near_sdk"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/pkg/util"
	"openedu-blockchain/services/nft"
	"regexp"
	"strings"
	"time"
)

const (
	NearDefaultNftCopies     = 1
	NearMintNftCostLogRegexp = `(?i)- Storage Cost: (\d+) yoctoNEAR\n- Gas Cost: (\d+) yoctoNEAR\n- Total Gas Cost: (\d+) yoctoNEAR`
	NearMintNftCostLogPrefix = "NFT Mint Transaction Details:"
)

var (
	NearDefaultMintNftDepositInNEARs = decimal.NewFromFloat(0.015) // 0.015 NEAR
	NearLearnerMintNftDepositInNEARs = decimal.NewFromFloat(0.015) // 0.015 NEAR
)

type NearMintNftService struct{}

func (s *NearMintNftService) DepositSponsorGas(
	account nft.Account,
	req *nft.DepositSponsorGasRequest,
) (*nft.DepositSponsorGasResponse, error) {

	contractID, method, args := s.getParamsForDepositSponsorGas(req)
	result := &nft.DepositSponsorGasResponse{
		ContractID: contractID,
		MethodName: method,
		InputData:  args,
		GasLimit:   openedu_near_sdk.DefaultGasUint64,
	}

	// Check sponsor amount greater than zero
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return result, fmt.Errorf("%w: amount must be greater than 0: amount(%s)", nft.ErrInvalidAmount, req.Amount.String())
	}

	// Get private key from account
	privateKey, err := s.getPrivateKey(account)
	if err != nil {
		return result, err
	}

	// Check balance is enough to sponsor
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	accountState, err := s.getAccountState(nodeURLs, account)
	if err != nil {
		if errors.Is(err, nft.ErrAccountNotExists) {
			return nil, fmt.Errorf("%w: the account does not exist: %w", nft.ErrInsufficientBalance, err)
		}
		return result, err
	}

	balance := accountState.Amount
	sponsorAmount := util.ParseNearAmount2Yocto(req.Amount)
	if sponsorAmount.GreaterThan(accountState.Amount) {
		return result, fmt.Errorf("%w: balance does not enough: balance(%s), amount(%s)",
			nft.ErrInsufficientBalance, balance.String(), sponsorAmount.String())
	}

	// Deposit sponsor gas fee
	txDetails, err := openedu_near_sdk.FunctionCallWithRetry(
		nodeURLs,
		account.GetAddress(),
		privateKey,
		contractID,
		method,
		args,
		openedu_near_sdk.DefaultGasUint64,
		*sponsorAmount.BigInt(),
	)
	if err != nil {
		if openedu_near_sdk.IsNotEnoughBalance(err) {
			b, cost, pErr := openedu_near_sdk.ParseBalanceAndCostFromMsg(err.Error())
			if pErr != nil {
				return result, fmt.Errorf("%w: insufficient gas fee", nft.ErrInsufficientGasFee)
			}

			return result, fmt.Errorf("%w: insufficient gas fee: balance(%s) gas(%s)",
				nft.ErrInsufficientGasFee, b.String(), cost.Sub(sponsorAmount).String())
		}
		return result, err
	}

	// Check transaction status
	time.Sleep(openedu_near_sdk.CheckTxnStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		account.GetAddress(),
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return result, fmt.Errorf("%w: check transaction status failed: %w", nft.ErrDepositSponsorGasFailed, err)
	}

	result.Status = models.TxStatusSuccess
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.TxHash = txDetails.TransactionOutcome.Id
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}

	return result, nil
}

func (s *NearMintNftService) WithdrawSponsorGas(
	account nft.Account,
	req *nft.WithdrawSponsorGasRequest,
) (*nft.WithdrawSponsorGasResponse, error) {

	contractID, method, args := s.getParamsForWithdrawSponsorGas(req)
	result := &nft.WithdrawSponsorGasResponse{
		ContractID: contractID,
		MethodName: method,
		InputData:  args,
		GasLimit:   openedu_near_sdk.DefaultGasUint64,
	}

	// Check withdraw amount greater than zero
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return result, fmt.Errorf("%w: amount must be greater than 0: amount(%s)", nft.ErrInvalidAmount, req.Amount.String())
	}

	// Get private key from account
	privateKey, err := s.getPrivateKey(account)
	if err != nil {
		return result, err
	}

	// Check balance is enough to withdraw
	sponsorBalance, err := s.getGasSponsorBalanceInYoctoNEARs(account, privateKey, req.CourseCuid, account.GetAddress(), req.IsMainnet)
	if err != nil {
		return result, err
	}

	withdrawAmount := util.ParseNearAmount2Yocto(req.Amount)
	if withdrawAmount.GreaterThan(sponsorBalance) {
		return result, fmt.Errorf("%w: sponsor balance does not enough: sponsor(%s), amount(%s)", nft.ErrInsufficientBalance, sponsorBalance.String(), withdrawAmount.String())
	}

	// Withdraw sponsor gas fee
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	txDetails, err := openedu_near_sdk.FunctionCallWithRetry(
		nodeURLs,
		account.GetAddress(),
		privateKey,
		contractID,
		method,
		args,
		openedu_near_sdk.DefaultGasUint64,
		*withdrawAmount.BigInt(),
	)
	if err != nil {
		return result, err
	}

	// Check transaction status
	time.Sleep(openedu_near_sdk.CheckTxnStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		account.GetAddress(),
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return result, fmt.Errorf("%w: check transaction status failed: %w", nft.ErrWithdrawSponsorGasFailed, err)
	}

	result.Status = models.TxStatusSuccess
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.TxHash = txDetails.TransactionOutcome.Id
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}
	return result, nil
}

func (s *NearMintNftService) MintNFT(account nft.Account, req *nft.MintNftRequest) (*nft.MintNftResponse, error) {
	result := &nft.MintNftResponse{}
	contractID, method, args, depositNEARs, depositInYoctoNEARs, err := s.getParamsForMintNft(account, req)
	if err != nil {
		return result, err
	}

	result.ContractID = contractID
	result.MethodName = method
	result.InputData = args
	result.GasLimit = openedu_near_sdk.DefaultGasUint64
	result.Deposit = depositNEARs
	result.Token = models.TokenNEAR

	// If gas fee payer is learner, account ID and private key is of the user's wallet
	// Else account ID and private key is of the NFT smart contract
	accountID, privateKey, err := s.validateAndGetAccountToMintNft(account, req)
	if err != nil {
		return result, err
	}

	// Mint NFT
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	txDetails, err := openedu_near_sdk.FunctionCallWithRetry(
		nodeURLs,
		accountID,
		privateKey,
		contractID,
		method,
		args,
		openedu_near_sdk.DefaultGasUint64,
		*depositInYoctoNEARs,
	)
	if err != nil {
		return result, err
	}

	// Check transaction status
	time.Sleep(openedu_near_sdk.CheckTxnStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		accountID,
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return result, fmt.Errorf("%w: check transaction status failed: %w", nft.ErrMintNFTFailed, err)
	}

	result.Status = models.TxStatusSuccess
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.TxHash = txDetails.TransactionOutcome.Id
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}

	for _, receipt := range txDetails.ReceiptsOutcome {
		for _, str := range receipt.Outcome.Logs {
			if strings.HasPrefix(str, NearMintNftCostLogPrefix) {
				log.Debugf("NearMintNftService::MintNFT find cost log: %s", str)
				if storageCost, gasCost, totalGasCost, pErr := s.parseMintNftCostLog(str); pErr != nil {
					log.Errorf("NearMintNftService::MintNFT parse cost log failed: %v", pErr)
				} else {
					result.StorageCost = storageCost
					result.GasCost = gasCost
					result.TotalGasCost = totalGasCost
				}
			}
		}
	}

	return result, nil
}

func (s *NearMintNftService) getNodeURLs(isMainnet bool) []string {
	return lo.If(isMainnet, setting.NearSetting.MainnetURLs).
		Else(setting.NearSetting.TestnetURLs)
}

func (s *NearMintNftService) getAccountState(nodeURLs []string, account nft.Account) (*openedu_near_sdk.AccountState, error) {
	state, err := openedu_near_sdk.GetAccountStateWithRetry(nodeURLs, account.GetAddress())
	if err == nil {
		return state, nil
	}
	if s.isAccountNotExistError(account, err) {
		return nil, fmt.Errorf("%w: %w", nft.ErrAccountNotExists, err)
	}
	return nil, fmt.Errorf("%w: %w", nft.ErrGetAccountStateFailed, err)
}

func (s *NearMintNftService) isAccountNotExistError(account nft.Account, err error) bool {
	return strings.Contains(err.Error(), fmt.Sprintf("account %s does not exist while viewing", account.GetAddress()))
}

func (s *NearMintNftService) getPrivateKey(account nft.Account) (ed25519.PrivateKey, error) {
	privateKeyStr, err := account.GetPrivateKey()
	if err != nil {
		return nil, fmt.Errorf("%w: %w", nft.ErrGetPrivateKeyFailed, err)
	}

	privateKey, err := util.Ed25519PrivateKeyFromString(util.Ed25519Prefix + privateKeyStr)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", nft.ErrParsePrivateKeyFailed, err)
	}

	return privateKey, nil
}

func (s *NearMintNftService) getParamsForDepositSponsorGas(req *nft.DepositSponsorGasRequest) (string, string, models.JSONB) {
	contractID := setting.NearSetting.TestnetNftContractID
	methodName := setting.NearSetting.TestnetNftDepositSponsorMethodName
	args := models.JSONB{
		"course_id": req.CourseCuid,
	}
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetNftContractID
		methodName = setting.NearSetting.MainnetNftDepositSponsorMethodName
	}
	return contractID, methodName, args
}

func (s *NearMintNftService) getParamsForGetSponsorBalance(isMainnet bool) (string, string) {
	contractID := setting.NearSetting.TestnetNftContractID
	methodName := setting.NearSetting.TestnetNftGetSponsorBalanceMethodName
	if isMainnet {
		contractID = setting.NearSetting.MainnetNftContractID
		methodName = setting.NearSetting.MainnetNftGetSponsorBalanceMethodName
	}
	return contractID, methodName
}

func (s *NearMintNftService) getGasSponsorBalanceInYoctoNEARs(
	account nft.Account,
	privateKey ed25519.PrivateKey,
	courseCuid string,
	courseOwnerAddress string,
	isMainnet bool,
) (decimal.Decimal, error) {

	nodeURLs := s.getNodeURLs(isMainnet)
	contractID, methodName := s.getParamsForGetSponsorBalance(isMainnet)
	balanceInYoctoNEARS, err := openedu_near_sdk.GetGasSponsorBalanceWithRetry(
		nodeURLs,
		privateKey,
		contractID,
		methodName,
		courseCuid,
		courseOwnerAddress,
	)
	if err != nil {
		if s.isAccountNotExistError(account, err) {
			return decimal.Zero, fmt.Errorf("%w: %w", nft.ErrAccountNotExists, err)
		}
		return decimal.Zero, fmt.Errorf("%w: %w", nft.ErrGetSponsorBalanceFailed, err)
	}

	return balanceInYoctoNEARS, nil
}

func (s *NearMintNftService) getParamsForWithdrawSponsorGas(
	req *nft.WithdrawSponsorGasRequest,
) (string, string, models.JSONB) {

	amountInYoctoNEARs := util.ParseNearAmount2Yocto(req.Amount)
	contractID := setting.NearSetting.TestnetNftContractID
	methodName := setting.NearSetting.TestnetNftWithdrawSponsorMethodName
	args := models.JSONB{
		"course_id": req.CourseCuid,
		"amount":    amountInYoctoNEARs.BigInt(),
	}
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetNftContractID
		methodName = setting.NearSetting.MainnetNftWithdrawSponsorMethodName
	}
	return contractID, methodName, args
}

func (s *NearMintNftService) getParamsForMintNft(
	account nft.Account,
	req *nft.MintNftRequest,
) (string, string, models.JSONB, decimal.Decimal, *big.Int, error) {

	contractID := lo.If(req.IsMainnet, setting.NearSetting.MainnetNftContractID).
		Else(setting.NearSetting.TestnetNftContractID)

	args := models.JSONB{
		"token_id":    req.TokenID,
		"receiver_id": account.GetAddress(),
		"token_metadata": map[string]interface{}{
			"title":       req.TokenMetadata.Title,
			"description": req.TokenMetadata.Description,
			"media":       req.TokenMetadata.MediaURL,
			"copies":      NearDefaultNftCopies,
		},
	}

	var depositInNEARs decimal.Decimal
	var methodName string
	switch req.GasFeePayer {
	case models.Platform:
		depositInNEARs = NearDefaultMintNftDepositInNEARs
		methodName = lo.If(req.IsMainnet, setting.NearSetting.MainnetNftMintMethodName).
			Else(setting.NearSetting.TestnetNftMintMethodName)

	case models.Creator:
		depositInNEARs = NearDefaultMintNftDepositInNEARs
		args["course_id"] = req.CourseCuid
		methodName = lo.If(req.IsMainnet, setting.NearSetting.TestnetNftMintWithSponsorMethodName).
			Else(setting.NearSetting.TestnetNftMintWithSponsorMethodName)

	default:
		depositInNEARs = NearLearnerMintNftDepositInNEARs
		signature, err := s.getSignatureBase64ToMintNft(account, req)
		if err != nil {
			return "", "", nil, decimal.Zero, nil, err
		}
		args["signature_base64"] = signature
		args["course_id"] = req.CourseCuid

		methodName = lo.If(req.IsMainnet, setting.NearSetting.TestnetNftMintWithSignatureMethodName).
			Else(setting.NearSetting.TestnetNftMintWithSignatureMethodName)
	}

	depositInYoctoNEARs, err := util.ParseNearAmountAsBigInt(depositInNEARs.String())
	if err != nil {
		return "", "", nil, decimal.Zero, nil, err
	}

	return contractID, methodName, args, depositInNEARs, depositInYoctoNEARs, nil
}

func (s *NearMintNftService) getSignatureBase64ToMintNft(
	account nft.Account,
	req *nft.MintNftRequest,
) (string, error) {

	signaturePrivateKeyStr := lo.If(req.IsMainnet, setting.NearSetting.MainnetNftSignaturePrivateKey).
		Else(setting.NearSetting.TestnetNftSignaturePrivateKey)

	privateKeyBytes, err := base64.StdEncoding.DecodeString(signaturePrivateKeyStr)
	if err != nil {
		return "", fmt.Errorf("%w: %w", nft.ErrDecodePrivateKeyFailed, err)
	}

	var signPrivateKey [64]byte
	copy(signPrivateKey[:], privateKeyBytes)

	message := fmt.Sprintf("%s:%s", req.CourseCuid, account.GetAddress())
	signature := sign.Sign(nil, []byte(message), &signPrivateKey)
	signatureOnly := signature[:sign.Overhead]
	return base64.StdEncoding.EncodeToString(signatureOnly), nil
}

func (s *NearMintNftService) validateAndGetAccountToMintNft(
	account nft.Account,
	req *nft.MintNftRequest,
) (string, ed25519.PrivateKey, error) {

	if req.GasFeePayer == models.Learner {
		privateKey, err := s.getPrivateKey(account)
		if err != nil {
			return "", nil, err
		}

		// Check balance is enough to mint NFT
		nodeURLs := s.getNodeURLs(req.IsMainnet)
		accountState, err := s.getAccountState(nodeURLs, account)
		if err != nil {
			if errors.Is(err, nft.ErrAccountNotExists) {
				return "", nil, fmt.Errorf("%w: the account does not exist: %w", nft.ErrInsufficientBalance, err)
			}
			return "", nil, err
		}

		balance := accountState.Amount
		if balance.LessThan(openedu_near_sdk.DefaultGasFeeInYocto) {
			return "", nil, fmt.Errorf("%w: balance does not enough: balance(%s), gas(%s)",
				nft.ErrInsufficientGasFee, balance.String(), openedu_near_sdk.DefaultGasFeeInYocto.String())
		}

		return account.GetAddress(), privateKey, nil
	}

	nftContractID := lo.If(req.IsMainnet, setting.NearSetting.MainnetNftContractID).
		Else(setting.NearSetting.TestnetNftContractID)

	nftContractPKey := lo.If(req.IsMainnet, setting.NearSetting.MainnetNftPrivateKey).
		Else(setting.NearSetting.TestnetNftPrivateKey)

	privateKey, err := util.Ed25519PrivateKeyFromString(nftContractPKey)
	if err != nil {
		return "", nil, fmt.Errorf("%w: %w", nft.ErrParsePrivateKeyFailed, err)
	}

	if req.GasFeePayer == models.Creator {
		// Check balance is enough to mint nft
		sponsorBalance, sErr := s.getGasSponsorBalanceInYoctoNEARs(account, privateKey, req.CourseCuid, req.CourseOwnerAddress, req.IsMainnet)
		if sErr != nil {
			return "", nil, sErr
		}

		if sponsorBalance.LessThan(NearDefaultMintNftDepositInNEARs) {
			return "", nil, fmt.Errorf("%w: sponsor balance does not enough: sponsor(%s), estimated_fee(%s)",
				nft.ErrInsufficientBalance, sponsorBalance.String(), NearDefaultMintNftDepositInNEARs.String())
		}
	}

	return nftContractID, privateKey, nil
}

func (s *NearMintNftService) parseMintNftCostLog(str string) (storageCost, gasCost, totalGasCost decimal.Decimal, err error) {
	pattern := regexp.MustCompile(NearMintNftCostLogRegexp)

	matches := pattern.FindStringSubmatch(str)
	if len(matches) != 4 {
		return decimal.Zero, decimal.Zero, decimal.Zero, fmt.Errorf("invalid transaction details format")
	}

	storageCost, err = decimal.NewFromString(matches[1])
	if err != nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, fmt.Errorf("failed to parse storage cost: %w", err)
	}

	gasCost, err = decimal.NewFromString(matches[2])
	if err != nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, fmt.Errorf("failed to parse gas cost: %w", err)
	}

	totalGasCost, err = decimal.NewFromString(matches[3])
	if err != nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, fmt.Errorf("failed to parse total gas cost: %w", err)
	}

	return storageCost, gasCost, totalGasCost, nil
}
