package nft

import "errors"

var (
	ErrAccountNotExists         = errors.New("account does not exist")
	ErrGetPrivateKeyFailed      = errors.New("get private key failed")
	ErrParsePrivateKeyFailed    = errors.New("parse private key failed")
	ErrGetAccountStateFailed    = errors.New("failed to get account state")
	ErrInsufficientGasFee       = errors.New("insufficient gas fee")
	ErrInsufficientBalance      = errors.New("insufficient balance")
	ErrInvalidAmount            = errors.New("invalid amount")
	ErrGetSponsorBalanceFailed  = errors.New("get sponsor balance failed")
	ErrDecodePrivateKeyFailed   = errors.New("decode private key failed")
	ErrDepositSponsorGasFailed  = errors.New("deposit sponsor gas failed")
	ErrWithdrawSponsorGasFailed = errors.New("withdraw sponsor gas failed")
	ErrMintNFTFailed            = errors.New("mint nft failed")

	// EVM
	ErrInvalidGasFeePayer    = errors.New("invalid gas fee payer")
	ErrGetEthClientFailed    = errors.New("failed to get ETH client")
	ErrGetContractFailed     = errors.New("failed to get contract")
	ErrContractNotConfigured = errors.New("contract address not configured")
	ErrTransactionFailed     = errors.New("transaction failed")
	ErrTransactionTimeout    = errors.New("transaction timeout")
	ErrExtractTokenIDFailed  = errors.New("failed to extract token ID")
	ErrGetMessageHashFailed  = errors.New("failed to get message hash")
	ErrGetEIP712DomainFailed = errors.New("failed to get EIP-712 domain")
	ErrSignMessageFailed     = errors.New("failed to sign message")
	ErrSignatureExpired      = errors.New("signature expired")
	ErrCreatePermitFailed    = errors.New("failed to create permit")
)
