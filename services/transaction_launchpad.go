package services

import (
	"errors"
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/services/launchpad"
	"strings"
)

func (s *TransactionService) InitLaunchpadPool(req *dto.InitLaunchpadPoolRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		WalletID:  req.WalletID,
		Status:    models.TxStatusPending,
		Type:      models.TxnInitLaunchpadPool,
		IsMainnet: req.IsMainnet,
		Props: models.TransactionProps{
			CoreTxIDs: []string{req.CoreTxID},
		},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionInitLaunchpadPoolFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	wallet, aErr := Wallet.FindByID(req.WalletID, &models.FindOneOptions{})
	if aErr != nil {
		msg := "Find the wallet ID " + req.WalletID + " failed: " + aErr.Error()
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = aErr.ErrCode
		txn.Response = models.JSONB{
			"code":    aErr.ErrCode,
			"message": msg,
		}
		aErr = e.NewError500(e.TransactionInitLaunchpadPoolFailed, msg)
		return
	}

	if resp, err := s.launchpadService.InitPool(wallet, &launchpad.InitPoolRequest{
		LaunchpadID:      req.LaunchpadID,
		Token:            req.Token,
		MinPledge:        req.MinPledge,
		FundingStartDate: req.FundingStartDate,
		FundingEndDate:   req.FundingEndDate,
		TargetFunding:    req.TargetFunding,
		IsMainnet:        req.IsMainnet,
	}); err != nil {
		if resp != nil {
			txn.Deposit = resp.Deposit
			txn.Token = resp.Token

			txn.ToAddress = resp.ContractID
			txn.ToNetwork = wallet.Network

			txn.FromAddress = wallet.Address
			txn.FromNetwork = wallet.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
		}

		txn, appErr = s.handleLaunchpadError(txn, err)
		return

	} else {
		txn.Deposit = resp.Deposit
		txn.Token = resp.Token
		txn.Props.PoolID = resp.PoolID

		txn.ToAddress = resp.ContractID
		txn.ToNetwork = wallet.Network

		txn.FromAddress = wallet.Address
		txn.FromNetwork = wallet.Network

		txn.MethodName = resp.MethodName
		txn.InputData = resp.InputData
		txn.ContractID = resp.ContractID

		txn.TxHash = resp.TxHash
		txn.BlockHash = resp.BlockHash
		txn.GasLimit = resp.GasLimit
		txn.GasBurnt = resp.GasBurnt
		txn.Nonce = resp.Nonce

		txn.Status = resp.Status
		switch resp.Status {
		case models.TxStatusSuccess:
			txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

		case models.TxStatusFailed:
			msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
				" on network " + strings.ToUpper(string(wallet.Network)) + " failed: " + resp.ErrorMsg
			txn.ErrorCode = e.TransactionInitLaunchpadPoolFailed
			txn.Response = s.newTxnResponse(e.TransactionInitLaunchpadPoolFailed, msg, resp.TxDetails)
			appErr = e.NewError500(e.TransactionInitLaunchpadPoolFailed, msg)
		}
	}
	return
}

func (s *TransactionService) ApproveLaunchpadPool(req *dto.ApproveLaunchpadPoolRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		Status:    models.TxStatusPending,
		Type:      models.TxnApproveLaunchpadPool,
		IsMainnet: req.IsMainnet,
		Props:     models.TransactionProps{},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionApproveLaunchpadPoolFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	if resp, err := s.launchpadService.ApprovePool(&launchpad.ApprovePoolRequest{
		PoolID:     req.PoolID,
		IsApproved: req.IsApproved,
		Network:    req.Network,
		IsMainnet:  req.IsMainnet,
	}); err != nil {
		if resp != nil {
			txn.Deposit = resp.Deposit
			txn.Token = resp.Token

			txn.ToAddress = resp.ContractID
			txn.ToNetwork = req.Network

			txn.FromAddress = resp.ContractID
			txn.FromNetwork = req.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
		}

		txn, appErr = s.handleLaunchpadError(txn, err)
		return

	} else {
		txn.Deposit = resp.Deposit
		txn.Token = resp.Token

		txn.ToAddress = resp.ContractID
		txn.ToNetwork = req.Network

		txn.FromAddress = resp.ContractID
		txn.FromNetwork = req.Network

		txn.MethodName = resp.MethodName
		txn.InputData = resp.InputData
		txn.ContractID = resp.ContractID

		txn.TxHash = resp.TxHash
		txn.BlockHash = resp.BlockHash
		txn.GasLimit = resp.GasLimit
		txn.GasBurnt = resp.GasBurnt
		txn.Nonce = resp.Nonce

		txn.Status = resp.Status
		switch resp.Status {
		case models.TxStatusSuccess:
			txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

		case models.TxStatusFailed:
			msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
				" on network " + strings.ToUpper(string(req.Network)) + " failed: " + resp.ErrorMsg
			txn.ErrorCode = e.TransactionApproveLaunchpadPoolFailed
			txn.Response = s.newTxnResponse(e.TransactionApproveLaunchpadPoolFailed, msg, resp.TxDetails)
			appErr = e.NewError500(e.TransactionApproveLaunchpadPoolFailed, msg)
		}
	}
	return
}

func (s *TransactionService) PledgeLaunchpad(req *dto.PledgeLaunchpadRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		WalletID:  req.WalletID,
		Status:    models.TxStatusPending,
		Type:      models.TxnPledgeLaunchpad,
		Token:     req.Token,
		Deposit:   req.Amount,
		IsMainnet: req.IsMainnet,
		Props: models.TransactionProps{
			CoreTxIDs: []string{req.CoreTxID},
		},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionPledgeLaunchpadFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	wallet, aErr := Wallet.FindByID(req.WalletID, &models.FindOneOptions{})
	if aErr != nil {
		msg := "Find the wallet ID " + req.WalletID + " failed: " + aErr.Error()
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = aErr.ErrCode
		txn.Response = models.JSONB{
			"code":    aErr.ErrCode,
			"message": msg,
		}
		aErr = e.NewError500(e.TransactionPledgeLaunchpadFailed, msg)
		return
	}

	if resp, err := s.launchpadService.Pledge(wallet, &launchpad.PledgeLaunchpadRequest{
		PoolID:    req.PoolID,
		Amount:    req.Amount,
		Token:     req.Token,
		IsMainnet: req.IsMainnet,
	}); err != nil {
		if resp != nil {
			txn.Deposit = resp.Deposit
			txn.Token = resp.Token

			txn.ToAddress = resp.ContractID
			txn.ToNetwork = wallet.Network

			txn.FromAddress = wallet.Address
			txn.FromNetwork = wallet.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
		}

		txn, appErr = s.handleLaunchpadError(txn, err)
		return

	} else {
		txn.Deposit = resp.Deposit
		txn.Token = resp.Token

		txn.ToAddress = resp.ContractID
		txn.ToNetwork = wallet.Network

		txn.FromAddress = wallet.Address
		txn.FromNetwork = wallet.Network

		txn.MethodName = resp.MethodName
		txn.InputData = resp.InputData
		txn.ContractID = resp.ContractID

		txn.TxHash = resp.TxHash
		txn.BlockHash = resp.BlockHash
		txn.GasLimit = resp.GasLimit
		txn.GasBurnt = resp.GasBurnt
		txn.Nonce = resp.Nonce

		txn.Status = resp.Status
		switch resp.Status {
		case models.TxStatusSuccess:
			txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

		case models.TxStatusFailed:
			msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
				" on network " + strings.ToUpper(string(wallet.Network)) + " failed: " + resp.ErrorMsg
			txn.ErrorCode = e.TransactionPledgeLaunchpadFailed
			txn.Response = s.newTxnResponse(e.TransactionPledgeLaunchpadFailed, msg, resp.TxDetails)
			appErr = e.NewError500(e.TransactionPledgeLaunchpadFailed, msg)
		}
	}
	return
}

func (s *TransactionService) UpdateLpPoolFundingTime(req *dto.UpdateLpPoolFundingTimeRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		Status:    models.TxStatusPending,
		Type:      models.TxnUpdateLpPoolFundingTime,
		IsMainnet: req.IsMainnet,
		Props:     models.TransactionProps{},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionApproveLaunchpadPoolFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	if resp, err := s.launchpadService.UpdatePoolFundingTime(&launchpad.UpdatePoolFundingTimeRequest{
		PoolID:           req.PoolID,
		FundingStartDate: req.FundingStartDate,
		FundingEndDate:   req.FundingEndDate,
		Network:          req.Network,
		IsMainnet:        req.IsMainnet,
	}); err != nil {
		if resp != nil {
			txn.Deposit = resp.Deposit
			txn.Token = resp.Token

			txn.ToAddress = resp.ContractID
			txn.ToNetwork = req.Network

			txn.FromAddress = resp.ContractID
			txn.FromNetwork = req.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
		}

		txn, appErr = s.handleLaunchpadError(txn, err)
		return

	} else {
		txn.Deposit = resp.Deposit
		txn.Token = resp.Token

		txn.ToAddress = resp.ContractID
		txn.ToNetwork = req.Network

		txn.FromAddress = resp.ContractID
		txn.FromNetwork = req.Network

		txn.MethodName = resp.MethodName
		txn.InputData = resp.InputData
		txn.ContractID = resp.ContractID

		txn.TxHash = resp.TxHash
		txn.BlockHash = resp.BlockHash
		txn.GasLimit = resp.GasLimit
		txn.GasBurnt = resp.GasBurnt
		txn.Nonce = resp.Nonce

		txn.Status = resp.Status
		switch resp.Status {
		case models.TxStatusSuccess:
			txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

		case models.TxStatusFailed:
			msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
				" on network " + strings.ToUpper(string(req.Network)) + " failed: " + resp.ErrorMsg
			txn.ErrorCode = e.TransactionApproveLaunchpadPoolFailed
			txn.Response = s.newTxnResponse(e.TransactionApproveLaunchpadPoolFailed, msg, resp.TxDetails)
			appErr = e.NewError500(e.TransactionApproveLaunchpadPoolFailed, msg)
		}
	}
	return
}

func (s *TransactionService) CancelLaunchpadPool(req *dto.CancelLpPoolRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		Status:    models.TxStatusPending,
		Type:      models.TxnCancelLaunchpad,
		IsMainnet: req.IsMainnet,
		Props:     models.TransactionProps{},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionApproveLaunchpadPoolFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	wallet, aErr := Wallet.FindByID(req.WalletID, &models.FindOneOptions{})
	if aErr != nil {
		msg := "Find the wallet ID " + req.WalletID + " failed: " + aErr.Error()
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = aErr.ErrCode
		txn.Response = models.JSONB{
			"code":    aErr.ErrCode,
			"message": msg,
		}
		aErr = e.NewError500(e.TransactionCancelLaunchpadPoolFailed, msg)
		return
	}

	if resp, err := s.launchpadService.CancelPool(wallet, &launchpad.CancelPoolRequest{
		PoolID:    req.PoolID,
		IsMainnet: req.IsMainnet,
	}); err != nil {
		if resp != nil {
			txn.Deposit = resp.Deposit
			txn.Token = resp.Token

			txn.FromAddress = wallet.Address
			txn.FromNetwork = wallet.Network

			txn.ToAddress = resp.ContractID
			txn.ToNetwork = wallet.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
		}

		txn, appErr = s.handleLaunchpadError(txn, err)
		return

	} else {
		txn.Deposit = resp.Deposit
		txn.Token = resp.Token

		txn.FromAddress = wallet.Address
		txn.FromNetwork = wallet.Network

		txn.ToAddress = resp.ContractID
		txn.ToNetwork = wallet.Network

		txn.MethodName = resp.MethodName
		txn.InputData = resp.InputData
		txn.ContractID = resp.ContractID

		txn.TxHash = resp.TxHash
		txn.BlockHash = resp.BlockHash
		txn.GasLimit = resp.GasLimit
		txn.GasBurnt = resp.GasBurnt
		txn.Nonce = resp.Nonce

		txn.Status = resp.Status
		switch resp.Status {
		case models.TxStatusSuccess:
			txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

		case models.TxStatusFailed:
			msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
				" on network " + strings.ToUpper(string(wallet.Network)) + " failed: " + resp.ErrorMsg
			txn.ErrorCode = e.TransactionCancelLaunchpadPoolFailed
			txn.Response = s.newTxnResponse(e.TransactionCancelLaunchpadPoolFailed, msg, resp.TxDetails)
			appErr = e.NewError500(e.TransactionCancelLaunchpadPoolFailed, msg)
		}
	}
	return
}

func (s *TransactionService) CheckLpFundingResult(req *dto.CheckLpFundingResultRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		Status:    models.TxStatusPending,
		Type:      models.TxnCheckLpFundingResult,
		IsMainnet: req.IsMainnet,
		Props:     models.TransactionProps{},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionCheckFundingResultFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	if resp, err := s.launchpadService.CheckFundingResult(&launchpad.CheckFundingRequest{
		PoolID:           req.PoolID,
		IsWaitingFunding: req.IsWaitingFunding,
		Network:          req.Network,
		IsMainnet:        req.IsMainnet,
	}); err != nil {
		if resp != nil {
			txn.Deposit = resp.Deposit
			txn.Token = resp.Token

			txn.FromAddress = resp.ContractID
			txn.FromNetwork = req.Network

			txn.ToAddress = resp.ContractID
			txn.ToNetwork = req.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
		}

		txn, appErr = s.handleLaunchpadError(txn, err)
		return

	} else {
		txn.Deposit = resp.Deposit
		txn.Token = resp.Token

		txn.FromAddress = resp.ContractID
		txn.FromNetwork = req.Network

		txn.ToAddress = resp.ContractID
		txn.ToNetwork = req.Network

		txn.MethodName = resp.MethodName
		txn.InputData = resp.InputData
		txn.ContractID = resp.ContractID

		txn.TxHash = resp.TxHash
		txn.BlockHash = resp.BlockHash
		txn.GasLimit = resp.GasLimit
		txn.GasBurnt = resp.GasBurnt
		txn.Nonce = resp.Nonce

		txn.Props.PoolID = resp.PoolID
		txn.Props.PoolStatus = resp.PoolStatus

		txn.Status = resp.Status
		switch resp.Status {
		case models.TxStatusSuccess:
			txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

		case models.TxStatusFailed:
			msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
				" on network " + strings.ToUpper(string(req.Network)) + " failed: " + resp.ErrorMsg
			txn.ErrorCode = e.TransactionCheckFundingResultFailed
			txn.Response = s.newTxnResponse(e.TransactionCheckFundingResultFailed, msg, resp.TxDetails)
			appErr = e.NewError500(e.TransactionCheckFundingResultFailed, msg)
		}
	}
	return
}

func (s *TransactionService) ContinueLpPartialFunds(req *dto.ContinueLpPartialFundRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		Status:    models.TxStatusPending,
		Type:      models.TxnContinueLpPartialFund,
		IsMainnet: req.IsMainnet,
		Props:     models.TransactionProps{},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionContinueLaunchpadFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	if resp, err := s.launchpadService.ContinueWithPartialFund(&launchpad.ContinueLpPartialFundRequest{
		PoolID:     req.PoolID,
		IsApproved: req.IsApproved,
		Network:    req.Network,
		IsMainnet:  req.IsMainnet,
	}); err != nil {
		if resp != nil {
			txn.Deposit = resp.Deposit
			txn.Token = resp.Token

			txn.FromAddress = resp.ContractID
			txn.FromNetwork = req.Network

			txn.ToAddress = resp.ContractID
			txn.ToNetwork = req.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
		}

		txn, appErr = s.handleLaunchpadError(txn, err)
		return

	} else {
		txn.Deposit = resp.Deposit
		txn.Token = resp.Token

		txn.FromAddress = resp.ContractID
		txn.FromNetwork = req.Network

		txn.ToAddress = resp.ContractID
		txn.ToNetwork = req.Network

		txn.MethodName = resp.MethodName
		txn.InputData = resp.InputData
		txn.ContractID = resp.ContractID

		txn.TxHash = resp.TxHash
		txn.BlockHash = resp.BlockHash
		txn.GasLimit = resp.GasLimit
		txn.GasBurnt = resp.GasBurnt
		txn.Nonce = resp.Nonce

		txn.Status = resp.Status
		switch resp.Status {
		case models.TxStatusSuccess:
			txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

		case models.TxStatusFailed:
			msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
				" on network " + strings.ToUpper(string(req.Network)) + " failed: " + resp.ErrorMsg
			txn.ErrorCode = e.TransactionContinueLaunchpadFailed
			txn.Response = s.newTxnResponse(e.TransactionContinueLaunchpadFailed, msg, resp.TxDetails)
			appErr = e.NewError500(e.TransactionContinueLaunchpadFailed, msg)
		}
	}
	return
}

func (s *TransactionService) SetLaunchpadFundingTime(req *dto.SetLpFundingTimeRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		Status:    models.TxStatusPending,
		Type:      models.TxnSetLpPoolFundingTime,
		IsMainnet: req.IsMainnet,
		Props:     models.TransactionProps{},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionApproveLaunchpadPoolFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	wallet, aErr := Wallet.FindByID(req.WalletID, &models.FindOneOptions{})
	if aErr != nil {
		msg := "Find the wallet ID " + req.WalletID + " failed: " + aErr.Error()
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = aErr.ErrCode
		txn.Response = models.JSONB{
			"code":    aErr.ErrCode,
			"message": msg,
		}
		aErr = e.NewError500(e.TransactionSetLaunchpadFundingTimeFailed, msg)
		return
	}

	if resp, err := s.launchpadService.SetFundingTime(wallet, &launchpad.SetPoolFundingTimeRequest{
		PoolID:              req.PoolID,
		FundingStartDate:    req.FundingStartDate,
		FundingDurationDays: req.FundingDurationDays,
		IsMainnet:           req.IsMainnet,
	}); err != nil {
		if resp != nil {
			txn.Deposit = resp.Deposit
			txn.Token = resp.Token

			txn.FromAddress = wallet.Address
			txn.FromNetwork = wallet.Network

			txn.ToAddress = resp.ContractID
			txn.ToNetwork = wallet.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
		}

		txn, appErr = s.handleLaunchpadError(txn, err)
		return

	} else {
		txn.Deposit = resp.Deposit
		txn.Token = resp.Token

		txn.FromAddress = wallet.Address
		txn.FromNetwork = wallet.Network

		txn.ToAddress = resp.ContractID
		txn.ToNetwork = wallet.Network

		txn.MethodName = resp.MethodName
		txn.InputData = resp.InputData
		txn.ContractID = resp.ContractID

		txn.TxHash = resp.TxHash
		txn.BlockHash = resp.BlockHash
		txn.GasLimit = resp.GasLimit
		txn.GasBurnt = resp.GasBurnt
		txn.Nonce = resp.Nonce

		txn.Status = resp.Status
		switch resp.Status {
		case models.TxStatusSuccess:
			txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

		case models.TxStatusFailed:
			msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
				" on network " + strings.ToUpper(string(wallet.Network)) + " failed: " + resp.ErrorMsg
			txn.ErrorCode = e.TransactionSetLaunchpadFundingTimeFailed
			txn.Response = s.newTxnResponse(e.TransactionSetLaunchpadFundingTimeFailed, msg, resp.TxDetails)
			appErr = e.NewError500(e.TransactionSetLaunchpadFundingTimeFailed, msg)
		}
	}
	return
}

func (s *TransactionService) WithdrawLaunchpadFundToCreator(req *dto.WithdrawLaunchpadFundToCreatorRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		Status:    models.TxStatusPending,
		Type:      models.TxnWithdrawLaunchpadFundToCreator,
		IsMainnet: req.IsMainnet,
		Props:     models.TransactionProps{},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionWithdrawLaunchpadFundToCreatorFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	if resp, err := s.launchpadService.WithdrawToCreator(&launchpad.WithdrawToCreatorRequest{
		PoolID:    req.PoolID,
		Token:     req.Token,
		Amount:    req.Amount,
		Network:   req.Network,
		IsMainnet: req.IsMainnet,
	}); err != nil {
		if resp != nil {
			txn.Deposit = resp.Deposit
			txn.Token = resp.Token

			txn.ToAddress = resp.ContractID
			txn.ToNetwork = req.Network

			txn.FromAddress = resp.ContractID
			txn.FromNetwork = req.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
		}

		txn, appErr = s.handleLaunchpadError(txn, err)
		return

	} else {
		txn.Deposit = resp.Deposit
		txn.Token = resp.Token

		txn.ToAddress = resp.ContractID
		txn.ToNetwork = req.Network

		txn.FromAddress = resp.ContractID
		txn.FromNetwork = req.Network

		txn.MethodName = resp.MethodName
		txn.InputData = resp.InputData
		txn.ContractID = resp.ContractID

		txn.TxHash = resp.TxHash
		txn.BlockHash = resp.BlockHash
		txn.GasLimit = resp.GasLimit
		txn.GasBurnt = resp.GasBurnt
		txn.Nonce = resp.Nonce

		txn.Status = resp.Status
		switch resp.Status {
		case models.TxStatusSuccess:
			txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

		case models.TxStatusFailed:
			msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
				" on network " + strings.ToUpper(string(req.Network)) + " failed: " + resp.ErrorMsg
			txn.ErrorCode = e.TransactionWithdrawLaunchpadFundToCreatorFailed
			txn.Response = s.newTxnResponse(e.TransactionWithdrawLaunchpadFundToCreatorFailed, msg, resp.TxDetails)
			appErr = e.NewError500(e.TransactionWithdrawLaunchpadFundToCreatorFailed, msg)
		}
	}
	return
}

func (s *TransactionService) ClaimLaunchpadRefund(req *dto.ClaimLaunchpadRefundRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		Status:    models.TxStatusPending,
		Type:      models.TxnTypeClaimLaunchpadRefund,
		Token:     req.Token,
		IsMainnet: req.IsMainnet,
		Props:     models.TransactionProps{},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionClaimLaunchpadRefundFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	wallet, aErr := Wallet.FindByID(req.WalletID, &models.FindOneOptions{})
	if aErr != nil {
		msg := "Find the wallet ID " + req.WalletID + " failed: " + aErr.Error()
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = aErr.ErrCode
		txn.Response = models.JSONB{
			"code":    aErr.ErrCode,
			"message": msg,
		}
		aErr = e.NewError500(e.TransactionClaimLaunchpadRefundFailed, msg)
		return
	}

	if resp, err := s.launchpadService.ClaimRefund(wallet, &launchpad.ClaimRefundRequest{
		PoolID:    req.PoolID,
		Token:     req.Token,
		IsMainnet: req.IsMainnet,
	}); err != nil {
		if resp != nil {
			txn.Deposit = resp.Deposit
			txn.Token = resp.Token

			txn.FromAddress = resp.ContractID
			txn.FromNetwork = wallet.Network

			txn.ToAddress = wallet.Address
			txn.ToNetwork = wallet.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
			txn.Deposit = resp.Deposit
		}

		txn, appErr = s.handleLaunchpadError(txn, err)
		return

	} else {
		txn.Deposit = resp.Deposit
		txn.Token = resp.Token

		txn.FromAddress = resp.ContractID
		txn.FromNetwork = wallet.Network

		txn.ToAddress = wallet.Address
		txn.ToNetwork = wallet.Network

		txn.MethodName = resp.MethodName
		txn.InputData = resp.InputData
		txn.ContractID = resp.ContractID
		txn.Deposit = resp.Deposit

		txn.TxHash = resp.TxHash
		txn.BlockHash = resp.BlockHash
		txn.GasLimit = resp.GasLimit
		txn.GasBurnt = resp.GasBurnt
		txn.Nonce = resp.Nonce

		txn.Status = resp.Status
		switch resp.Status {
		case models.TxStatusSuccess:
			txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

		case models.TxStatusFailed:
			msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
				" on network " + strings.ToUpper(string(wallet.Network)) + " failed: " + resp.ErrorMsg
			txn.ErrorCode = e.TransactionClaimLaunchpadRefundFailed
			txn.Response = s.newTxnResponse(e.TransactionClaimLaunchpadRefundFailed, msg, resp.TxDetails)
			appErr = e.NewError500(e.TransactionClaimLaunchpadRefundFailed, msg)
		}
	}
	return
}

func (s *TransactionService) UpdateLaunchpadPoolStatus(req *dto.UpdateLaunchpadPoolStatusRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		Status:    models.TxStatusPending,
		Type:      models.TxnUpdateLaunchpadPoolStatus,
		IsMainnet: req.IsMainnet,
		Props:     models.TransactionProps{},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionUpdateLaunchpadPoolStatusFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	if resp, err := s.launchpadService.UpdatePoolStatus(&launchpad.UpdatePoolStatusRequest{
		PoolID:    req.PoolID,
		Status:    req.Status,
		Network:   req.Network,
		IsMainnet: req.IsMainnet,
	}); err != nil {
		if resp != nil {
			txn.Deposit = resp.Deposit
			txn.Token = resp.Token

			txn.FromAddress = resp.ContractID
			txn.FromNetwork = req.Network

			txn.ToAddress = resp.ContractID
			txn.ToNetwork = req.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
			txn.Deposit = resp.Deposit
		}

		txn, appErr = s.handleLaunchpadError(txn, err)
		return

	} else {
		txn.Deposit = resp.Deposit
		txn.Token = resp.Token

		txn.FromAddress = resp.ContractID
		txn.FromNetwork = req.Network

		txn.ToAddress = resp.ContractID
		txn.ToNetwork = req.Network

		txn.MethodName = resp.MethodName
		txn.InputData = resp.InputData
		txn.ContractID = resp.ContractID
		txn.Deposit = resp.Deposit

		txn.TxHash = resp.TxHash
		txn.BlockHash = resp.BlockHash
		txn.GasLimit = resp.GasLimit
		txn.GasBurnt = resp.GasBurnt
		txn.Nonce = resp.Nonce

		txn.Status = resp.Status
		switch resp.Status {
		case models.TxStatusSuccess:
			txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

		case models.TxStatusFailed:
			msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
				" on network " + strings.ToUpper(string(req.Network)) + " failed: " + resp.ErrorMsg
			txn.ErrorCode = e.TransactionUpdateLaunchpadPoolStatusFailed
			txn.Response = s.newTxnResponse(e.TransactionUpdateLaunchpadPoolStatusFailed, msg, resp.TxDetails)
			appErr = e.NewError500(e.TransactionUpdateLaunchpadPoolStatusFailed, msg)
		}
	}
	return
}

func (s *TransactionService) handleLaunchpadError(transaction *models.Transaction, err error) (*models.Transaction, *e.AppError) {
	if err == nil {
		return transaction, nil
	}

	switch {
	case errors.Is(err, launchpad.ErrAccountNotExists):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientBalance
		transaction.Response = models.JSONB{
			"code":    e.TransactionInsufficientBalance,
			"message": "Account does not enough balance to storage deposit: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionInsufficientBalance, "Account does not exist to deposit sponsor gas fee: "+err.Error())

	case errors.Is(err, launchpad.ErrInsufficientBalance):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientBalance
		transaction.Response = models.JSONB{
			"code":    e.TransactionInsufficientBalance,
			"message": "Account does not enough balance to storage deposit: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionInsufficientBalance, "Account does not enough balance to deposit sponsor gas fee: "+err.Error())

	case errors.Is(err, launchpad.ErrInsufficientGasFee):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientGasFee
		transaction.Response = models.JSONB{
			"code":    e.TransactionInsufficientGasFee,
			"message": "Account does not enough balance to cover the gas fee: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionInsufficientGasFee, "Account does not enough balance to cover the gas fee: "+err.Error())
	}

	switch transaction.Type {
	case models.TxnInitLaunchpadPool:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInitLaunchpadPoolFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionInitLaunchpadPoolFailed,
			"message": "Init launchpad pool failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionDepositSponsorGasFailed, "Init launchpad pool failed: "+err.Error())

	case models.TxnApproveLaunchpadPool:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionApproveLaunchpadPoolFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionApproveLaunchpadPoolFailed,
			"message": "Approve pool status before funding failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionApproveLaunchpadPoolFailed, "Approve pool status before funding failed: "+err.Error())

	case models.TxnUpdateLpPoolFundingTime:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionUpdateLaunchpadPoolFundingTimeFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionUpdateLaunchpadPoolFundingTimeFailed,
			"message": "Update launchpad pool funding time failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionUpdateLaunchpadPoolFundingTimeFailed, "Update launchpad pool funding time failed: "+err.Error())

	case models.TxnPledgeLaunchpad:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionPledgeLaunchpadFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionPledgeLaunchpadFailed,
			"message": "Pledge launchpad failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionPledgeLaunchpadFailed, "Pledge launchpad failed: "+err.Error())

	case models.TxnCheckLpFundingResult:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionCheckFundingResultFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionCheckFundingResultFailed,
			"message": "Check launchpad funding result failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionCheckFundingResultFailed, "Check launchpad funding result failed: "+err.Error())

	case models.TxnCancelLaunchpad:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionCancelLaunchpadPoolFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionCancelLaunchpadPoolFailed,
			"message": "Cancel launchpad pool failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionCancelLaunchpadPoolFailed, "Cancel launchpad pool failed: "+err.Error())

	case models.TxnSetLpPoolFundingTime:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionSetLaunchpadFundingTimeFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionSetLaunchpadFundingTimeFailed,
			"message": "Set launchpad pool funding failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionSetLaunchpadFundingTimeFailed, "Set launchpad pool funding failed: "+err.Error())

	case models.TxnTypeClaimLaunchpadRefund:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionClaimLaunchpadRefundFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionClaimLaunchpadRefundFailed,
			"message": "Claim launchpad refund failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionClaimLaunchpadRefundFailed, "Claim launchpad refund failed: "+err.Error())

	case models.TxnUpdateLaunchpadPoolStatus:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionUpdateLaunchpadPoolStatusFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionUpdateLaunchpadPoolStatusFailed,
			"message": "Update launchpad pool status failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionUpdateLaunchpadPoolStatusFailed, "Update launchpad pool status failed: "+err.Error())

	case models.TxnContinueLpPartialFund:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionContinueLaunchpadFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionContinueLaunchpadFailed,
			"message": "Continue launchpad with partial target funding failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionContinueLaunchpadFailed, "Continue launchpad with partial target funding failed: "+err.Error())

	default:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInvalidRequest
		transaction.Response = models.JSONB{
			"code":    e.TransactionCancelLaunchpadPoolFailed,
			"message": "Cancel launchpad pool failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionInvalidRequest, "Execute transaction type "+string(transaction.Type)+" failed: "+err.Error())
	}
}
