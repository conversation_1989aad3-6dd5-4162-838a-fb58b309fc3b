package crypto_transfer

import (
	"github.com/shopspring/decimal"
	"openedu-blockchain/models"
	"time"
)

const (
	DelayBeforeCheckStatus = 200 * time.Millisecond
)

type TransferStrategy interface {
	Transfer(account Account, req *TransferRequest) (*TransferResult, error)
	BatchTransfer(account Account, req *BatchTransferRequest) ([]*TransferResult, error)
	GetAccountInfo(req *GetAccountInfoRequest) (*GetAccountInfoResponse, error)
}

type Account interface {
	GetAddress() string
	GetNetwork() models.BlockchainNetwork
	GetPublicKey() string
	GetPrivateKey() (string, error)
}

type TransferRequest struct {
	ToAddress string                   `json:"to_address"`
	ToNetwork models.BlockchainNetwork `json:"to_network"`
	TokenID   string                   `json:"token_id"`
	Token     models.BlockchainToken   `json:"token"`
	Amount    decimal.Decimal          `json:"amount"`
	IsMainnet bool                     `json:"is_mainnet"`
}

type TransferRecipient struct {
	Amount    decimal.Decimal `json:"amount"`
	ToAddress string          `json:"to_address"`
}

type BatchTransferRequest struct {
	ToNetwork  models.BlockchainNetwork `json:"to_network"`
	Recipients []*TransferRecipient     `json:"recipients"`
	TokenID    string                   `json:"token_id"`
	Token      models.BlockchainToken   `json:"token"`
	IsMainnet  bool                     `json:"is_mainnet"`
}

type TransferResult struct {
	ContractID string                   `json:"contract_id"`
	MethodName string                   `json:"method_name"`
	InputData  models.JSONB             `json:"input_data"`
	BlockHash  string                   `json:"block_hash"`
	TxHash     string                   `json:"tx_hash"`
	GasLimit   uint64                   `json:"gas_limit"`
	GasBurnt   uint64                   `json:"gas_burnt"`
	Nonce      uint64                   `json:"nonce"`
	TxDetails  interface{}              `json:"tx_details"`
	Status     models.TransactionStatus `json:"status"`
	ErrorMsg   string                   `json:"error_msg"`
}

type GetAccountInfoRequest struct {
	Network   models.BlockchainNetwork `json:"network"`
	Address   string                   `json:"address"`
	IsMainnet bool                     `json:"is_mainnet"`
}

type GetAccountInfoResponse struct {
	Network     models.BlockchainNetwork `json:"network"`
	Address     string                   `json:"address"`
	AccountInfo interface{}              `json:"account_info"`
}
