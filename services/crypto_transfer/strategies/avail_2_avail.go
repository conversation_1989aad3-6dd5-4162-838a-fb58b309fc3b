package strategies

import (
	"fmt"
	"github.com/availproject/avail-go-sdk/src/sdk"
	gsrpc "github.com/centrifuge/go-substrate-rpc-client/v4"
	"github.com/centrifuge/go-substrate-rpc-client/v4/signature"
	gsrpctypes "github.com/centrifuge/go-substrate-rpc-client/v4/types"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/vedhavyas/go-subkey/v2"
	"math/big"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/openedu_avail_sdk"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/services/crypto_transfer"
)

const (
	AvailNetworkID = 42
)

type Avail2AvailTransferService struct{}

type AvailBlockData struct {
	Block AvailBlock `json:"block"`
}

type AvailBlock struct {
	Extrinsics []AvailExtrinsic `json:"extrinsics"`
}

type AvailExtrinsic struct {
	Hash     string `json:"hash"`
	GasLimit uint64 `json:"gasLimit"`
	GasBurnt uint64 `json:"gasBurnt"`
}

type AvailAccountInfo struct {
	Nonce       gsrpctypes.U32 `json:"nonce"`
	Consumers   gsrpctypes.U32 `json:"consumers"`
	Providers   gsrpctypes.U32 `json:"providers"`
	Sufficients gsrpctypes.U32 `json:"sufficients"`
	Data        struct {
		Free       gsrpctypes.U128 `json:"free"`
		Reserved   gsrpctypes.U128 `json:"reserved"`
		MiscFrozen gsrpctypes.U128 `json:"miscFrozen"`
		Flags      gsrpctypes.U128 `json:"feeFrozen"`
	} `json:"data"`
}

func (s *Avail2AvailTransferService) Transfer(
	account crypto_transfer.Account,
	req *crypto_transfer.TransferRequest,
) (*crypto_transfer.TransferResult, error) {

	if req.Token == models.TokenAVAIL {
		return s.transferNativeToken(account, req)
	}
	return s.transferFungibleToken(account, req)
}

func (s *Avail2AvailTransferService) BatchTransfer(
	account crypto_transfer.Account,
	req *crypto_transfer.BatchTransferRequest,
) ([]*crypto_transfer.TransferResult, error) {

	if req.Token == models.TokenAVAIL {
		return s.batchTransferNativeToken(account, req)
	}
	return s.batchTransferFungibleToken(account, req)
}

func (s *Avail2AvailTransferService) GetAccountInfo(
	req *crypto_transfer.GetAccountInfoRequest,
) (*crypto_transfer.GetAccountInfoResponse, error) {

	nodeURLs := s.getNodeURLs(req.IsMainnet)
	substrateApi, err := gsrpc.NewSubstrateAPI(nodeURLs[0])
	if err != nil {
		return nil, fmt.Errorf("%w: init new sdk with api url %s failed: %w",
			crypto_transfer.ErrConnectRPCFailed, nodeURLs[0], err)
	}

	_, pubKeyBytes, dErr := subkey.SS58Decode(req.Address)
	if dErr != nil {
		return nil, fmt.Errorf("%w: decode recipient address failed: %w", crypto_transfer.ErrInvalidAddress, dErr)
	}

	metadata, err := substrateApi.RPC.State.GetMetadataLatest()
	if err != nil {
		return nil, fmt.Errorf("%w: get metadata latest failed: %w",
			crypto_transfer.ErrGetAccountStateFailed, err)
	}

	key, err := gsrpctypes.CreateStorageKey(metadata, "System", "Account", pubKeyBytes, nil)
	if err != nil {
		return nil, fmt.Errorf("%w: init new sdk with api url %s failed: %w",
			crypto_transfer.ErrConnectRPCFailed, nodeURLs[0], err)
	}

	var accountInfo AvailAccountInfo
	sOk, err := substrateApi.RPC.State.GetStorageLatest(key, &accountInfo)
	if err != nil {
		return nil, fmt.Errorf("%w: get storage latest failed: %w",
			crypto_transfer.ErrGetAccountStateFailed, err)
	} else if !sOk {
		accountInfo.Data.Free = gsrpctypes.NewU128(*big.NewInt(0))
		accountInfo.Data.Reserved = gsrpctypes.NewU128(*big.NewInt(0))
		accountInfo.Data.MiscFrozen = gsrpctypes.NewU128(*big.NewInt(0))
		accountInfo.Data.Flags = gsrpctypes.NewU128(*big.NewInt(0))
	}

	return &crypto_transfer.GetAccountInfoResponse{
		Network:     req.Network,
		Address:     req.Address,
		AccountInfo: accountInfo,
	}, nil
}

func (s *Avail2AvailTransferService) transferNativeToken(
	account crypto_transfer.Account,
	req *crypto_transfer.TransferRequest,
) (*crypto_transfer.TransferResult, error) {

	result := &crypto_transfer.TransferResult{
		MethodName: openedu_avail_sdk.TransferKeepAliveMethodName,
		InputData:  models.JSONB{},
	}

	// Check transfer amount greater than zero
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return result, fmt.Errorf("%w: amount must be greater than 0: amount(%s)",
			crypto_transfer.ErrInvalidAmount, req.Amount.String())
	}

	// Get keyring pair
	seedPhrase, err := s.getSeedPhrase(account)
	if err != nil {
		return result, err
	}

	keyringPair, err := signature.KeyringPairFromSecret(seedPhrase, AvailNetworkID)
	if err != nil {
		return result, fmt.Errorf("%w: create keyring pair from seed phrase failed: %w",
			crypto_transfer.ErrGetAccountStateFailed, err)
	}

	// Check sender account is whether enough balance to send
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	substrateApi, err := gsrpc.NewSubstrateAPI(nodeURLs[0])
	if err != nil {
		return result, fmt.Errorf("%w: init new sdk with api url %s failed: %w",
			crypto_transfer.ErrConnectRPCFailed, nodeURLs[0], err)
	}

	metadata, err := substrateApi.RPC.State.GetMetadataLatest()
	if err != nil {
		return result, fmt.Errorf("%w: get metadata latest failed: %w",
			crypto_transfer.ErrGetAccountStateFailed, err)
	}

	key, err := gsrpctypes.CreateStorageKey(metadata, "System", "Account", keyringPair.PublicKey, nil)
	if err != nil {
		return result, fmt.Errorf("%w: init new sdk with api url %s failed: %w",
			crypto_transfer.ErrConnectRPCFailed, nodeURLs[0], err)
	}

	var accountInfo gsrpctypes.AccountInfo
	sOk, err := substrateApi.RPC.State.GetStorageLatest(key, &accountInfo)
	if err != nil || !sOk {
		return result, fmt.Errorf("%w: get storage latest failed: %w",
			crypto_transfer.ErrGetAccountStateFailed, err)
	}

	transferAmount := parseAvailAmount2Decimal(req.Amount)
	balance := accountInfo.Data.Free
	if balance.Cmp(transferAmount) < 0 {
		return result, fmt.Errorf(
			"%w: balance does not enough: balance(%s), amount(%s)",
			crypto_transfer.ErrInsufficientBalance,
			balance.String(),
			transferAmount.String(),
		)
	}

	api, err := sdk.NewSDK(nodeURLs[0])
	if err != nil {
		return nil, fmt.Errorf("%w: init new sdk with api url %s failed: %w", crypto_transfer.ErrConnectRPCFailed, nodeURLs[0], err)
	}

	bondAmountUCompact := gsrpctypes.NewUCompact(transferAmount)
	txDetails, err := openedu_avail_sdk.TransferKeepAlive(api, seedPhrase, sdk.BlockInclusion, req.ToAddress, bondAmountUCompact)
	if err != nil {
		return nil, fmt.Errorf("%w: transfer keep alive failed: %w", crypto_transfer.ErrTransferFailed, err)
	}

	result.Status = models.TxStatusPending
	result.BlockHash = txDetails.BlockHash
	result.TxHash = txDetails.TxHash
	result.GasBurnt = txDetails.FeePaid.Uint64()
	result.Nonce = txDetails.Nonce
	result.TxDetails = txDetails
	switch txDetails.Status {
	case openedu_avail_sdk.Success:
		result.Status = models.TxStatusSuccess

	case openedu_avail_sdk.Failed:
		result.Status = models.TxStatusFailed
		result.ErrorMsg = "please check transaction on explorer to get more detail" // TODO
	}
	return result, nil
}

func (s *Avail2AvailTransferService) transferFungibleToken(
	_ crypto_transfer.Account,
	_ *crypto_transfer.TransferRequest,
) (*crypto_transfer.TransferResult, error) {
	return nil, fmt.Errorf("%w: Avail2AvailTransferService.transferFungibleToken is not implemented", crypto_transfer.ErrNotImplemented)
}

func (s *Avail2AvailTransferService) batchTransferNativeToken(
	account crypto_transfer.Account,
	req *crypto_transfer.BatchTransferRequest,
) ([]*crypto_transfer.TransferResult, error) {

	result := &crypto_transfer.TransferResult{
		MethodName: openedu_avail_sdk.UtilityBatchMethodName,
		InputData:  models.JSONB{},
	}

	seedPhrase, err := s.getSeedPhrase(account)
	if err != nil {
		return []*crypto_transfer.TransferResult{result}, err
	}

	nodeURLs := s.getNodeURLs(req.IsMainnet)
	api, err := sdk.NewSDK(nodeURLs[0])
	if err != nil {
		return []*crypto_transfer.TransferResult{result}, fmt.Errorf("%w: init new sdk with api url %s failed: %w",
			crypto_transfer.ErrConnectRPCFailed, nodeURLs[0], err)
	}

	var recipients []*openedu_avail_sdk.BatchTransferRecipient
	for _, recipient := range req.Recipients {
		transferAmount := recipient.Amount.Mul(decimal.New(1, 18))
		bondAmountUCompact := gsrpctypes.NewUCompact(transferAmount.BigInt())
		recipients = append(recipients, &openedu_avail_sdk.BatchTransferRecipient{
			Address: recipient.ToAddress,
			Amount:  bondAmountUCompact,
		})
	}

	estimatedFee, err := openedu_avail_sdk.EstimateBatchTransferKeepAlive(api, seedPhrase, recipients)
	if err != nil {
		return []*crypto_transfer.TransferResult{result}, fmt.Errorf("%w: transfer keep alive failed: %w",
			crypto_transfer.ErrTransferFailed, err)
	}

	keyringPair, err := signature.KeyringPairFromSecret(seedPhrase, 42)
	if err != nil {
		return []*crypto_transfer.TransferResult{result}, fmt.Errorf("%w: create keyring pair from seed phrase failed: %w",
			crypto_transfer.ErrGetAccountStateFailed, err)
	}

	metadata, err := api.RPC.State.GetMetadataLatest()
	if err != nil {
		return []*crypto_transfer.TransferResult{result}, fmt.Errorf("%w: get metadata latest failed: %w",
			crypto_transfer.ErrGetAccountStateFailed, err)
	}

	key, err := gsrpctypes.CreateStorageKey(metadata, "System", "Account", keyringPair.PublicKey, nil)
	if err != nil {
		return []*crypto_transfer.TransferResult{result},
			fmt.Errorf("%w: init new sdk with api url %s failed: %w", crypto_transfer.ErrConnectRPCFailed, nodeURLs[0], err)
	}

	var accountInfo gsrpctypes.AccountInfo
	sOk, err := api.RPC.State.GetStorageLatest(key, &accountInfo)
	if err != nil || !sOk {
		return []*crypto_transfer.TransferResult{result}, fmt.Errorf("%w: get storage latest failed: %w",
			crypto_transfer.ErrGetAccountStateFailed, err)
	}

	totalAmountInDecimal := decimal.Zero
	for _, recipient := range req.Recipients {
		totalAmountInDecimal = totalAmountInDecimal.Add(recipient.Amount)
	}

	transferAmount := parseAvailAmount2Decimal(totalAmountInDecimal)
	balance := accountInfo.Data.Free
	if balance.Cmp(transferAmount.Add(transferAmount, estimatedFee)) < 0 {
		return []*crypto_transfer.TransferResult{result}, fmt.Errorf(
			"%w: balance does not enough: balance(%s), amount(%s)",
			crypto_transfer.ErrInsufficientBalance,
			balance.String(),
			transferAmount.String(),
		)
	}

	// Send AVAIL
	txDetails, err := openedu_avail_sdk.BatchTransferKeepAlive(api, seedPhrase, sdk.BlockInclusion, recipients)
	if err != nil {
		return []*crypto_transfer.TransferResult{result}, fmt.Errorf("%w: transfer keep alive failed: %w",
			crypto_transfer.ErrTransferFailed, err)
	}

	result.Status = models.TxStatusPending
	result.BlockHash = txDetails.BlockHash
	result.TxHash = txDetails.TxHash
	result.GasBurnt = txDetails.FeePaid.Uint64()
	result.Nonce = txDetails.Nonce
	result.TxDetails = txDetails
	switch txDetails.Status {
	case openedu_avail_sdk.Success:
		result.Status = models.TxStatusSuccess

	case openedu_avail_sdk.Failed:
		result.Status = models.TxStatusFailed
		result.ErrorMsg = "please check transaction on explorer to get more detail" // TODO
	}
	return []*crypto_transfer.TransferResult{result}, nil
}

func (s *Avail2AvailTransferService) batchTransferFungibleToken(
	_ crypto_transfer.Account,
	_ *crypto_transfer.BatchTransferRequest,
) ([]*crypto_transfer.TransferResult, error) {
	return nil, fmt.Errorf("%w: Avail2AvailTransferService.batchTransferFungibleToken is not implemented", crypto_transfer.ErrNotImplemented)
}

func (s *Avail2AvailTransferService) getNodeURLs(isMainnet bool) []string {
	return lo.If(isMainnet, setting.AvailSetting.MainnetURLs).
		Else(setting.AvailSetting.TestnetURLs)
}

func (s *Avail2AvailTransferService) getSeedPhrase(account crypto_transfer.Account) (string, error) {
	privateKeyStr, err := account.GetPrivateKey()
	if err != nil {
		return "", fmt.Errorf("%w: %w", crypto_transfer.ErrGetPrivateKeyFailed, err)
	}

	return privateKeyStr, nil
}

// parseAvailAmount2Decimal parses AVAIL amount to decimal amount
func parseAvailAmount2Decimal(amount decimal.Decimal) *big.Int {
	return amount.Mul(decimal.New(1, 18)).BigInt()
}
