package services

import (
	"errors"
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/services/crypto_payment"
	"strings"
)

func (s *TransactionService) Payment(req *dto.PaymentRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		WalletID:  req.WalletID,
		Type:      models.TxnTypePayment,
		Deposit:   req.Amount,
		Token:     req.Token,
		IsMainnet: req.IsMainnet,
		Props: models.TransactionProps{
			CoreTxIDs: []string{req.CoreTxID},
		},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionPaymentFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	wallet, aErr := Wallet.FindByID(req.WalletID, &models.FindOneOptions{})
	if aErr != nil {
		msg := "Find the wallet ID " + req.WalletID + " failed: " + aErr.Error()
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = aErr.ErrCode
		txn.Response = models.JSONB{
			"code":    aErr.ErrCode,
			"message": msg,
		}
		aErr = e.NewError500(e.TransactionPaymentFailed, msg)
		return
	}

	txn.FromAddress = wallet.Address
	txn.FromNetwork = wallet.Network
	resp, err := s.paymentService.Payment(wallet, &crypto_payment.PaymentRequest{
		CourseCUID:          req.CourseCUID,
		Token:               req.Token,
		Amount:              req.Amount,
		Fee:                 req.Fee,
		ProfitDistributions: req.ProfitDistributions,
		IsMainnet:           req.IsMainnet,
	})
	if err != nil {
		txn, appErr = s.handlePaymentError(txn, err)
		return
	}

	txn.ContractID = resp.ContractID
	txn.MethodName = resp.MethodName
	txn.InputData = resp.InputData
	txn.TxHash = resp.TxHash
	txn.BlockHash = resp.BlockHash
	txn.Nonce = resp.Nonce
	txn.GasLimit = resp.GasLimit
	txn.GasBurnt = resp.GasBurnt
	txn.Status = resp.Status
	switch txn.Status {
	case models.TxStatusSuccess:
		txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

	case models.TxStatusFailed:
		msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
			" on network " + strings.ToUpper(string(wallet.Network)) + " failed: " + resp.ErrorMsg
		txn.ErrorCode = e.TransactionPaymentFailed
		txn.Response = s.newTxnResponse(e.TransactionPaymentFailed, msg, resp.TxDetails)
		appErr = e.NewError500(e.TransactionPaymentFailed, msg)
	}
	return
}

func (s *TransactionService) ClaimEarning(req *dto.ClaimEarningRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		WalletID:  req.WalletID,
		Type:      models.TxnTypeClaimEarning,
		Deposit:   req.Amount,
		Token:     req.Token,
		IsMainnet: req.IsMainnet,
		Props: models.TransactionProps{
			CoreTxIDs: []string{req.CoreTxID},
		},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionClaimEarningFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	wallet, aErr := Wallet.FindByID(req.WalletID, &models.FindOneOptions{})
	if aErr != nil {
		msg := "Find the wallet ID " + req.WalletID + " failed: " + aErr.Error()
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = aErr.ErrCode
		txn.Response = models.JSONB{
			"code":    aErr.ErrCode,
			"message": msg,
		}
		aErr = e.NewError500(e.TransactionClaimEarningFailed, msg)
		return
	}

	txn.FromAddress = wallet.Address
	txn.FromNetwork = wallet.Network
	resp, err := s.paymentService.ClaimEarning(wallet, &crypto_payment.ClaimEarningRequest{
		Token:     req.Token,
		Amount:    req.Amount,
		Fee:       req.Fee,
		IsMainnet: req.IsMainnet,
	})
	if err != nil {
		txn, appErr = s.handleClaimEarningError(txn, err)
		return
	}

	txn.ContractID = resp.ContractID
	txn.MethodName = resp.MethodName
	txn.InputData = resp.InputData
	txn.TxHash = resp.TxHash
	txn.BlockHash = resp.BlockHash
	txn.Nonce = resp.Nonce
	txn.GasLimit = resp.GasLimit
	txn.GasBurnt = resp.GasBurnt
	txn.Status = resp.Status
	switch txn.Status {
	case models.TxStatusSuccess:
		txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

	case models.TxStatusFailed:
		msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
			" on network " + strings.ToUpper(string(wallet.Network)) + " failed: " + resp.ErrorMsg
		txn.ErrorCode = e.TransactionClaimEarningFailed
		txn.Response = s.newTxnResponse(e.TransactionClaimEarningFailed, msg, resp.TxDetails)
		appErr = e.NewError500(e.TransactionClaimEarningFailed, msg)
	}
	return
}

func (s *TransactionService) handlePaymentError(transaction *models.Transaction, err error) (*models.Transaction, *e.AppError) {
	if err == nil {
		return transaction, nil
	}

	switch {
	case errors.Is(err, crypto_payment.ErrConnectRPCFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionPaymentFailed
		transaction.Response = s.newTxnResponse(
			e.TransactionPaymentFailed,
			"Connect RPC to validate transfer request failed: "+err.Error(),
			nil,
		)
		return transaction, e.NewError500(e.TransactionPaymentFailed, "Connect RPC to validate transfer request failed: "+err.Error())

	case errors.Is(err, crypto_payment.ErrAccountNotExists):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientGasFee
		transaction.Response = s.newTxnResponse(
			e.TransactionInsufficientGasFee,
			"Account does not exist to cover gas fee: "+err.Error(),
			nil,
		)
		return transaction, e.NewError500(e.TransactionInsufficientGasFee, "Account does not exist to cover gas fee: "+err.Error())

	case errors.Is(err, crypto_payment.ErrGetAccountStateFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionPaymentFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionPaymentFailed,
			"message": "Get account state to check balance and gas fee failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionPaymentFailed, "Get account state to check balance and gas fee failed: "+err.Error())

	case errors.Is(err, crypto_payment.ErrInsufficientGasFee):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientGasFee
		transaction.Response = models.JSONB{
			"code":    e.TransactionInsufficientGasFee,
			"message": "Account does not enough balance to cover gas fee: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionInsufficientGasFee, "Account does not enough balance to cover gas fee: "+err.Error())

	case errors.Is(err, crypto_payment.ErrInsufficientBalance):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientBalance
		transaction.Response = models.JSONB{
			"code":    e.TransactionInsufficientBalance,
			"message": "Account does not enough balance to payment: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionInsufficientBalance, "Account does not enough balance to payment: "+err.Error())

	case errors.Is(err, crypto_payment.ErrGetFtBalanceFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionPaymentFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionPaymentFailed,
			"message": "Check account balance before transfer failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionPaymentFailed, "Check account balance before transfer failed: "+err.Error())

	case errors.Is(err, crypto_payment.ErrGetStorageBalanceFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionPaymentFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionPaymentFailed,
			"message": "Check account storage deposit balance failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionPaymentFailed, "Check account storage deposit balance failed: "+err.Error())

	case errors.Is(err, crypto_payment.ErrGetStorageBoundsFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionPaymentFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionPaymentFailed,
			"message": "Get deposit storage bounds of token failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionPaymentFailed, "Get deposit storage bounds of token failed: "+err.Error())

	case errors.Is(err, crypto_payment.ErrInsufficientBalanceToStorageDeposit):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientGasFee
		transaction.Response = models.JSONB{
			"code":    e.TransactionInsufficientGasFee,
			"message": "Account does not enough balance to storage deposit: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionInsufficientGasFee, "Account does not enough balance to storage deposit: "+err.Error())

	case errors.Is(err, crypto_payment.ErrStorageDepositFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionPaymentFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionPaymentFailed,
			"message": "Storage deposit for recipient account failed: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionPaymentFailed, "Storage deposit for recipient account failed: "+err.Error())

	default:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionPaymentFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionPaymentFailed,
			"message": "Payment failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionPaymentFailed, "Payment failed: "+err.Error())
	}
}

func (s *TransactionService) handleClaimEarningError(transaction *models.Transaction, err error) (*models.Transaction, *e.AppError) {
	if err == nil {
		return transaction, nil
	}

	switch {
	case errors.Is(err, crypto_payment.ErrConnectRPCFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionClaimEarningFailed
		transaction.Response = s.newTxnResponse(
			e.TransactionClaimEarningFailed,
			"Connect RPC to validate transfer request failed: "+err.Error(),
			nil,
		)
		return transaction, e.NewError500(e.TransactionClaimEarningFailed, "Connect RPC to validate transfer request failed: "+err.Error())

	case errors.Is(err, crypto_payment.ErrAccountNotExists):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientGasFee
		transaction.Response = s.newTxnResponse(
			e.TransactionInsufficientGasFee,
			"Account does not exist to cover gas fee: "+err.Error(),
			nil,
		)
		return transaction, e.NewError500(e.TransactionInsufficientGasFee, "Account does not exist to cover gas fee: "+err.Error())

	case errors.Is(err, crypto_payment.ErrGetAccountStateFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionClaimEarningFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionClaimEarningFailed,
			"message": "Get account state to check balance and gas fee failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionClaimEarningFailed, "Get account state to check balance and gas fee failed: "+err.Error())

	case errors.Is(err, crypto_payment.ErrInsufficientGasFee):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientGasFee
		transaction.Response = models.JSONB{
			"code":    e.TransactionInsufficientGasFee,
			"message": "Account does not enough balance to cover gas fee: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionInsufficientGasFee, "Account does not enough balance to cover gas fee: "+err.Error())

	case errors.Is(err, crypto_payment.ErrInsufficientBalance):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientBalance
		transaction.Response = models.JSONB{
			"code":    e.TransactionInsufficientBalance,
			"message": "Account does not enough balance to payment: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionInsufficientBalance, "Account does not enough balance to payment: "+err.Error())

	case errors.Is(err, crypto_payment.ErrGetFtBalanceFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionClaimEarningFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionClaimEarningFailed,
			"message": "Check account balance before transfer failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionClaimEarningFailed, "Check account balance before transfer failed: "+err.Error())

	case errors.Is(err, crypto_payment.ErrGetStorageBalanceFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionClaimEarningFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionClaimEarningFailed,
			"message": "Check account storage deposit balance failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionClaimEarningFailed, "Check account storage deposit balance failed: "+err.Error())

	case errors.Is(err, crypto_payment.ErrGetStorageBoundsFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionClaimEarningFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionClaimEarningFailed,
			"message": "Get deposit storage bounds of token failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionClaimEarningFailed, "Get deposit storage bounds of token failed: "+err.Error())

	case errors.Is(err, crypto_payment.ErrInsufficientBalanceToStorageDeposit):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientGasFee
		transaction.Response = models.JSONB{
			"code":    e.TransactionInsufficientGasFee,
			"message": "Account does not enough balance to storage deposit: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionInsufficientGasFee, "Account does not enough balance to storage deposit: "+err.Error())

	case errors.Is(err, crypto_payment.ErrStorageDepositFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionClaimEarningFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionClaimEarningFailed,
			"message": "Storage deposit for recipient account failed: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionClaimEarningFailed, "Storage deposit for recipient account failed: "+err.Error())

	default:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionClaimEarningFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionClaimEarningFailed,
			"message": "Payment failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionClaimEarningFailed, "Claim earning failed: "+err.Error())
	}
}
