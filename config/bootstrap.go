package config

import (
	"context"
	"golang.org/x/sync/semaphore"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/log"
	"runtime"
)

func InitSystem() {
	InitSystemConfigs()
}

func InitSystemConfigs() {
	maxWorkers := runtime.GOMAXPROCS(0)
	sem := semaphore.NewWeighted(int64(maxWorkers))
	ctx := context.Background()
	for _, cfg := range GetDefaultSystemConfigsParams() {
		if err := sem.Acquire(ctx, 1); err != nil {
			log.Fatalf("Acquire semaphore to init config %s failed: %v", cfg.Key, err)
		}

		go func(cfg *InitConfigParams) {
			defer sem.Release(1)
			if err := models.InitConfig(cfg.Key, cfg.Val, cfg.Type, nil); err != nil {
				log.Fatalf("Init default config key %s failed: %v", cfg.Key, err)
			}
		}(&cfg)
	}

	if err := sem.Acquire(ctx, int64(maxWorkers)); err != nil {
		log.Fatalf("Acquire semaphore to init default configs failed: %v", err)
	}
}
