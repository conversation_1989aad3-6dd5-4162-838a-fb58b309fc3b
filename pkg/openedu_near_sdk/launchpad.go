package openedu_near_sdk

import (
	"crypto/ed25519"
	"encoding/json"
	"fmt"
	"github.com/aurora-is-near/near-api-go"
	"github.com/cenkalti/backoff/v4"
	"github.com/shopspring/decimal"
	"openedu-blockchain/pkg/log"
)

type VotingPowerEntry struct {
	Amount      int64   `json:"amount"`
	VotingPower float64 `json:"voting_power"`
}

type UserVotingPower struct {
	Record VotingPowerEntry `json:"record"`
	UserId string           `json:"user_id"`
}

type PoolDetails struct {
	CampaignId        string          `json:"campaign_id"`
	CreatorId         string          `json:"creator_id"`
	MinMultiplePledge int64           `json:"min_multiple_pledge"`
	PoolId            int64           `json:"pool_id"`
	StakingAmount     decimal.Decimal `json:"staking_amount"`
	Status            string          `json:"status"`
	TargetFunding     int64           `json:"target_funding"`
	TimeEndPledge     int64           `json:"time_end_pledge"`
	TimeStartPledge   int64           `json:"time_start_pledge"`
	TokenId           string          `json:"token_id"`
	TotalBalance      int64           `json:"total_balance"`
}

func GetVotingPowersByPool(account *near.Account, contractID, methodName, poolID string) ([]*UserVotingPower, error) {
	resp, err := account.ViewFunction(
		contractID,
		methodName,
		[]byte(fmt.Sprintf(`{"pool_id": %s}`, poolID)),
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("call %s method on contract %s failed: %w", methodName, contractID, err)
	}

	respMap, ok := resp.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("parse %s response failed: type assertion interface{} to map[string]interface[} failed: resp is of type %T", methodName, resp)
	}

	result, found := respMap["result"]
	if !found {
		return nil, fmt.Errorf("parse %s response failed: result not found", methodName)
	}

	resultArray, ok := result.([]interface{})
	if !ok {
		return nil, fmt.Errorf("parse %s response failed: type assertion interface{} to []interface[} failed: result is of type %T", methodName, result)
	}

	bytes := make([]byte, len(resultArray))
	for i, v := range resultArray {
		// Convert json.Number to byte
		jsonNum, ok := v.(json.Number)
		if !ok {
			return nil, fmt.Errorf("parse %s response failed: type asertion interface{} to json.Number failed: v is of type %T", methodName, v)
		}

		byteValue, err := jsonNum.Int64()
		if err != nil {
			return nil, fmt.Errorf("parse %s response failed: convert json.Number to int64 failed: %w", methodName, err)
		}

		bytes[i] = byte(byteValue)
	}

	var votingPowersByPool []*UserVotingPower
	if fErr := json.Unmarshal(bytes, &votingPowersByPool); fErr != nil {
		return nil, fmt.Errorf("parse %s response failed: %v", methodName, fErr)
	}

	return votingPowersByPool, nil
}

func GetVotingPowersByPoolWithRetry(
	nodeURLs []string,
	accountID string,
	privateKey ed25519.PrivateKey,
	contractID, methodName string,
	poolID string,
) ([]*UserVotingPower, error) {
	var votingPowers []*UserVotingPower
	var err error

	expBackoff := NewDefaultExpBackoffForViewFunc()
	err = backoff.Retry(func() error {
		for _, nodeURL := range nodeURLs {
			conn := near.NewConnection(nodeURL)
			account := near.LoadAccountWithPrivateKey(conn, accountID, privateKey)
			votingPowers, err = GetVotingPowersByPool(account, contractID, methodName, poolID)
			if err == nil {
				return nil
			}
			log.Errorf("Failed to get list voting powers of pool ID %s on node %s: %v, trying next node...", poolID, nodeURL, err)
		}
		return err
	}, expBackoff)

	if err != nil {
		return nil, fmt.Errorf("failed to get list voting powers of pool ID %s after retries: %w", accountID, err)
	}
	return votingPowers, nil
}

func GetPoolDetails(account *near.Account, contractID, methodName, poolID string) (*PoolDetails, error) {
	resp, err := account.ViewFunction(
		contractID,
		methodName,
		[]byte(fmt.Sprintf(`{"pool_id": %s}`, poolID)),
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("call %s method on contract %s failed: %w", methodName, contractID, err)
	}

	respMap, ok := resp.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("parse %s response failed: type assertion interface{} to map[string]interface[} failed: resp is of type %T", methodName, resp)
	}

	result, found := respMap["result"]
	if !found {
		return nil, fmt.Errorf("parse %s response failed: result not found", methodName)
	}

	resultArray, ok := result.([]interface{})
	if !ok {
		return nil, fmt.Errorf("parse %s response failed: type assertion interface{} to []interface[} failed: result is of type %T", methodName, result)
	}

	bytes := make([]byte, len(resultArray))
	for i, v := range resultArray {
		// Convert json.Number to byte
		jsonNum, ok := v.(json.Number)
		if !ok {
			return nil, fmt.Errorf("parse %s response failed: type asertion interface{} to json.Number failed: v is of type %T", methodName, v)
		}

		byteValue, err := jsonNum.Int64()
		if err != nil {
			return nil, fmt.Errorf("parse %s response failed: convert json.Number to int64 failed: %w", methodName, err)
		}

		bytes[i] = byte(byteValue)
	}

	var poolDetails *PoolDetails
	if fErr := json.Unmarshal(bytes, &poolDetails); fErr != nil {
		return nil, fmt.Errorf("parse %s response failed: %v", methodName, fErr)
	}

	return poolDetails, nil
}

func GetPoolDetailsWithRetry(
	nodeURLs []string,
	accountID string,
	privateKey ed25519.PrivateKey,
	contractID, methodName string,
	poolID string,
) (*PoolDetails, error) {
	var poolDetails *PoolDetails
	var err error

	expBackoff := NewDefaultExpBackoffForViewFunc()
	err = backoff.Retry(func() error {
		for _, nodeURL := range nodeURLs {
			conn := near.NewConnection(nodeURL)
			account := near.LoadAccountWithPrivateKey(conn, accountID, privateKey)
			poolDetails, err = GetPoolDetails(account, contractID, methodName, poolID)
			if err == nil {
				return nil
			}
			log.Errorf("Failed to get details of pool ID %s on node %s: %v, trying next node...", poolID, nodeURL, err)
		}
		return err
	}, expBackoff)

	if err != nil {
		return nil, fmt.Errorf("failed to get details of pool ID %s after retries: %w", accountID, err)
	}
	return poolDetails, nil
}
