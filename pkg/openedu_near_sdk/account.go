package openedu_near_sdk

import (
	"crypto/ed25519"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"github.com/aurora-is-near/near-api-go"
	"github.com/btcsuite/btcutil/base58"
	"github.com/cenkalti/backoff/v4"
	"github.com/cosmos/go-bip39"
	"github.com/shopspring/decimal"
	"openedu-blockchain/pkg/log"
	"regexp"
)

type Account struct {
	AccountID  string
	PublicKey  string
	PrivateKey string
}

type AccountState struct {
	Amount        decimal.Decimal `json:"amount"`
	BlockHash     string          `json:"block_hash"`
	BlockHeight   int64           `json:"block_height"`
	CodeHash      string          `json:"code_hash"`
	Locked        string          `json:"locked"`
	StoragePaidAt int64           `json:"storage_paid_at"`
	StorageUsage  int64           `json:"storage_usage"`
}

func CreateImplicitAccount(seedPhrase, secretString string) (*Account, error) {
	// Generate ED25519 key pair from seed phrase and secret string
	publicKey, privateKey, err := GenerateKeyPair(seedPhrase, secretString)
	if err != nil {
		return nil, fmt.Errorf("error generating public key: %v", err)
	}

	// Convert the public key to account ID
	accountID := PublicKeyToAccountID(publicKey)

	// Encode keys in base58 for better readability
	base58PublicKey := base58.Encode(publicKey)
	base58PrivateKey := base58.Encode(privateKey)

	return &Account{
		AccountID:  accountID,
		PublicKey:  base58PublicKey,
		PrivateKey: base58PrivateKey,
	}, nil
}

func GetAccountState(conn *near.Connection, accountID string) (*AccountState, error) {
	respMap, err := conn.GetAccountState(accountID)
	if err != nil {
		return nil, fmt.Errorf("call get account state failed: %w", err)
	}

	accState, err := MapToStruct[AccountState](respMap)
	if err != nil {
		return nil, fmt.Errorf("parse account state response failed: %w", err)
	}

	return &accState, nil
}

// GenerateKeyPair generates an ED25519 key pair from a seed phrase and secret string
func GenerateKeyPair(seedPhrase, secretString string) (ed25519.PublicKey, ed25519.PrivateKey, error) {
	// ValidateSingleTransfer and normalize the seed phrase
	if !bip39.IsMnemonicValid(seedPhrase) {
		return nil, nil, fmt.Errorf("invalid seed phrase")
	}

	// Combine seed phrase and secret string
	combined := seedPhrase + secretString

	// Generate seed from the combined string
	seed := sha256.Sum256([]byte(combined))

	// Generate ED25519 key pair from the seed
	privateKey := ed25519.NewKeyFromSeed(seed[:])
	publicKey := privateKey.Public().(ed25519.PublicKey)

	return publicKey, privateKey, nil
}

// PublicKeyToAccountID converts a public key to its corresponding account ID
func PublicKeyToAccountID(publicKey ed25519.PublicKey) string {
	return hex.EncodeToString(publicKey)
}

func GetAccountStateWithRetry(nodeURLs []string, accountID string) (*AccountState, error) {
	var accountState *AccountState
	var err error

	expBackoff := NewDefaultExpBackoffForViewFunc()
	err = backoff.Retry(func() error {
		for _, nodeURL := range nodeURLs {
			conn := near.NewConnection(nodeURL)
			accountState, err = GetAccountState(conn, accountID)
			if err == nil {
				return nil
			}
			log.Errorf("Failed to state for NEAR account ID %s on node %s: %v, trying next node...", accountID, nodeURL, err)
		}
		return err
	}, expBackoff)

	if err != nil {
		return nil, fmt.Errorf("failed to get account state after retries: %w", err)
	}
	return accountState, nil
}

func IsValidAddress(address string) bool {
	if len(address) < 2 || len(address) > 64 {
		return false
	}

	if len(address) == 64 {
		matched, _ := regexp.MatchString("^[0-9a-fA-F]{64}$", address)
		return matched
	}

	matched, _ := regexp.MatchString(`^(([a-z0-9]+[-_])*[a-z0-9]+)(\.[a-z0-9]+([-_][a-z0-9]+)*)*$`, address)
	return matched
}
