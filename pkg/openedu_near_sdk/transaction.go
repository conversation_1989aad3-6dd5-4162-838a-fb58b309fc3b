package openedu_near_sdk

import (
	"crypto/ed25519"
	"encoding/json"
	"fmt"
	"github.com/aurora-is-near/near-api-go"
	"github.com/cenkalti/backoff/v4"
	"math/big"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/util"
)

const NotEnoughBalancePrefix = ""

type ReceiptsOutcome struct {
	BlockHash string `json:"block_hash"`
	Id        string `json:"id"`
	Outcome   struct {
		ExecutorId string   `json:"executor_id"`
		GasBurnt   int64    `json:"gas_burnt"`
		Logs       []string `json:"logs"`
		Metadata   struct {
			GasProfile []struct {
				Cost         string `json:"cost"`
				CostCategory string `json:"cost_category"`
				GasUsed      string `json:"gas_used"`
			} `json:"gas_profile"`
			Version int `json:"version"`
		} `json:"metadata"`
		ReceiptIds []string `json:"receipt_ids"`
		Status     struct {
			SuccessValue string `json:"SuccessValue"`
		} `json:"status"`
		TokensBurnt string `json:"tokens_burnt"`
	} `json:"outcome"`
	Proof []struct {
		Direction string `json:"direction"`
		Hash      string `json:"hash"`
	} `json:"proof"`
}

type TxStatus struct {
	SuccessValue *string `json:"SuccessValue"`
	Failure      *struct {
		ActionError struct {
			Index int `json:"index"`
			Kind  struct {
				FunctionCallError struct {
					ExecutionError string `json:"ExecutionError"`
				} `json:"FunctionCallError"`
			} `json:"kind"`
		} `json:"ActionError"`
	} `json:"Failure"`
}

type Transaction struct {
	Actions []struct {
		FunctionCall struct {
			Args       string `json:"args"`
			Deposit    string `json:"deposit"`
			Gas        int64  `json:"gas"`
			MethodName string `json:"method_name"`
		} `json:"FunctionCall"`
	} `json:"actions"`
	Hash        string `json:"hash"`
	Nonce       uint64 `json:"nonce"`
	PriorityFee int    `json:"priority_fee"`
	PublicKey   string `json:"public_key"`
	ReceiverId  string `json:"receiver_id"`
	Signature   string `json:"signature"`
	SignerId    string `json:"signer_id"`
}

type TransactionOutcome struct {
	BlockHash string `json:"block_hash"`
	Id        string `json:"id"`
	Outcome   struct {
		ExecutorId string        `json:"executor_id"`
		GasBurnt   uint64        `json:"gas_burnt"`
		Logs       []interface{} `json:"logs"`
		Metadata   struct {
			GasProfile interface{} `json:"gas_profile"`
			Version    int         `json:"version"`
		} `json:"metadata"`
		ReceiptIds []string `json:"receipt_ids"`
		Status     struct {
			SuccessReceiptId string `json:"SuccessReceiptId"`
		} `json:"status"`
		TokensBurnt string `json:"tokens_burnt"`
	} `json:"outcome"`
	Proof []struct {
		Direction string `json:"direction"`
		Hash      string `json:"hash"`
	} `json:"proof"`
}

type TransactionDetails struct {
	FinalExecutionStatus string             `json:"final_execution_status"`
	ReceiptsOutcome      []*ReceiptsOutcome `json:"receipts_outcome"`
	Status               TxStatus           `json:"status"`
	Transaction          Transaction        `json:"transaction"`
	TransactionOutcome   TransactionOutcome `json:"transaction_outcome"`
}

func (t *TransactionDetails) IsFailed() bool {
	return t.Status.Failure != nil
}

func GetTransactionDetailsWithWait(
	nodeURLs []string,
	txHash string,
	senderAccountId string,
	waitUntil near.TxExecutionStatus,

) (*TransactionDetails, error) {
	var txDetails TransactionDetails
	var respMap map[string]interface{}
	var err error

	for _, nodeURL := range nodeURLs {
		conn := near.NewConnection(nodeURL)

		respMap, err = conn.GetTransactionDetailsWithWait(txHash, senderAccountId, waitUntil)
		if err != nil {
			err = fmt.Errorf("call get transaction details with wait failed: %w", err)
			continue
		}

		txDetails, err = util.MapToStruct[TransactionDetails](respMap)
		if err != nil {
			err = fmt.Errorf("parse transaction details response failed: %w", err)
			continue
		} else {
			break
		}
	}

	if err != nil {
		return nil, err
	}

	return &txDetails, nil
}

func FunctionCall(
	account *near.Account,
	contractID string,
	method string,
	args interface{},
	gas uint64,
	deposit big.Int,

) (*TransactionDetails, error) {
	bytes, err := json.Marshal(args)
	if err != nil {
		return nil, fmt.Errorf("parse args to bytes error: %w", err)
	}

	respMap, err := account.FunctionCall(contractID, method, bytes, gas, deposit)
	if err != nil {
		return nil, fmt.Errorf("function call method %s on contract %s failed: %w", method, contractID, err)
	}

	txDetails, err := util.MapToStruct[TransactionDetails](respMap)
	if err != nil {
		return nil, fmt.Errorf("parse transaction details response failed: %w", err)
	}

	return &txDetails, nil
}

func FunctionCallWithRetry(
	nodeURLs []string,
	accountID string,
	privateKey ed25519.PrivateKey,
	contractID string,
	method string,
	args interface{},
	gas uint64,
	deposit big.Int,
) (*TransactionDetails, error) {

	var txDetails *TransactionDetails
	var err error

	expBackoff := NewDefaultExpBackoffForCallFunc()
	err = backoff.Retry(func() error {
		for _, nodeURL := range nodeURLs {
			conn := near.NewConnection(nodeURL)
			account := near.LoadAccountWithPrivateKey(conn, accountID, privateKey)
			txDetails, err = FunctionCall(account, contractID, method, args, gas, deposit)
			if err == nil {
				return nil
			} else if IsNotEnoughBalance(err) {
				return err
			}
			log.Errorf("Failed to function call method %s on contract %s on node %s: %v, trying next node...", method, contractID, nodeURL, err)
		}
		return err
	}, expBackoff)
	if err != nil {
		return nil, err
	}

	return txDetails, nil
}