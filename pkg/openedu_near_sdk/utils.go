package openedu_near_sdk

import (
	"encoding/json"
	"fmt"
	"github.com/cenkalti/backoff/v4"
	"github.com/shopspring/decimal"
	"regexp"
	"strings"
	"time"
)

func MapToStruct[T any](m map[string]interface{}) (T, error) {
	var result T
	jsonBytes, err := json.Marshal(m)
	if err != nil {
		return result, fmt.Errorf("error marshaling map to JSON: %w", err)
	}

	if err = json.Unmarshal(jsonBytes, &result); err != nil {
		return result, fmt.Errorf("error unmarshaling JSON to struct: %w", err)
	}

	return result, nil
}

func NewDefaultExpBackoffForCallFunc() *backoff.ExponentialBackOff {
	expBackoff := backoff.NewExponentialBackOff()
	expBackoff.InitialInterval = 200 * time.Millisecond
	expBackoff.MaxInterval = 2 * time.Second
	expBackoff.MaxElapsedTime = 30 * time.Second
	return expBackoff
}

func NewDefaultExpBackoffForViewFunc() *backoff.ExponentialBackOff {
	expBackoff := backoff.NewExponentialBackOff()
	expBackoff.InitialInterval = 100 * time.Millisecond
	expBackoff.MaxInterval = 2 * time.Second
	expBackoff.MaxElapsedTime = 10 * time.Second
	return expBackoff
}

func IsNotEnoughBalance(err error) bool {
	return strings.Contains(err.Error(), NotEnoughBalancePrefix)
}

func ParseBalanceAndCostFromMsg(msg string) (balance, cost decimal.Decimal, err error) {
	// Regular expression to match balance and cost values
	// Looking for patterns like balance:900000000000000000000000 cost:988098220812645944388252
	re := regexp.MustCompile(`balance:(\d+)\s+cost:(\d+)`)
	matches := re.FindStringSubmatch(msg)

	if len(matches) != 3 {
		return decimal.Zero, decimal.Zero, fmt.Errorf("could not extract balance and cost from the log")
	}

	balanceRaw := matches[1]
	costRaw := matches[2]

	balance, err = decimal.NewFromString(balanceRaw)
	if err != nil {
		return decimal.Zero, decimal.Zero, fmt.Errorf("failed to parse balance: %v", err)
	}

	cost, err = decimal.NewFromString(costRaw)
	if err != nil {
		return decimal.Zero, decimal.Zero, fmt.Errorf("failed to parse cost: %v", err)
	}

	return balance, cost, nil
}
