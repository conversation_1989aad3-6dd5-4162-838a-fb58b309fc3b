package openedu_near_sdk

import (
	"github.com/shopspring/decimal"
	"time"
)

const (
	NEARNominationExp              = 24
	NEARToYoctoMultiplier          = 1e24
	CheckTxnStatusDelay            = 200 * time.Millisecond
	DefaultGasUint64               = 300_000_000_000_000 // 300 Tgas
	FtBalanceOfMethodName          = "ft_balance_of"
	StorageBalanceOfMethodName     = "storage_balance_of"
	StorageBalanceBoundsMethodName = "storage_balance_bounds"
	StorageDepositMethodName       = "storage_deposit"
	TransferMethodName             = "TRANSFER"
	FtTransferMethodName           = "ft_transfer"
	FtTransferCallMethodName       = "ft_transfer_call"
)

var DefaultGasFeeInYocto = decimal.NewFromFloat(0.2).Mul(decimal.NewFromFloat(NEARToYoctoMultiplier)) // 0.2NEAR
