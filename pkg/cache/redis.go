package cache

import (
	"context"
	"encoding/json"
	"errors"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/setting"
	"time"

	"github.com/redis/go-redis/v9"
)

type Redis struct {
	prefix string
	client *redis.Client
}

func newRedis() *Redis {
	rdb := redis.NewClient(&redis.Options{
		Addr:            setting.RedisSetting.Host,
		Password:        setting.RedisSetting.Password,
		MaxIdleConns:    setting.RedisSetting.MaxIdle,
		MaxActiveConns:  setting.RedisSetting.MaxActive,
		ConnMaxIdleTime: setting.RedisSetting.IdleTimeout,
	})
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if _, err := rdb.Ping(ctx).Result(); err != nil {
		log.Fatalf("Failed to ping to Redis: %v", err)
	}
	return &Redis{client: rdb, prefix: setting.RedisSetting.PrefixChannel}
}

func (c *Redis) Set(key string, value interface{}, ttl time.Duration) error {
	cacheKey := c.prefix + key
	ctx := context.Background()
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return c.client.Set(ctx, cacheKey, data, ttl).Err()
}

func (c *Redis) Get(key string, value interface{}) error {
	cacheKey := c.prefix + key
	ctx := context.Background()
	data, err := c.client.Get(ctx, cacheKey).Bytes()
	if errors.Is(err, redis.Nil) {
		return errors.New("key not found")
	} else if err != nil {
		return err
	}
	return json.Unmarshal(data, value)
}

func (c *Redis) Delete(key string) error {
	ctx := context.Background()
	return c.client.Del(ctx, c.prefix+key).Err()
}

func (c *Redis) DeleteByPrefix(prefix string) error {
	ctx := context.Background()
	keys, err := c.client.Keys(ctx, c.prefix+prefix+"*").Result()
	if err != nil {
		return err
	}
	log.Debugf("Keys to delete: %v", keys)
	if len(keys) > 0 {
		return c.client.Del(ctx, keys...).Err()
	}
	return nil
}

func (c *Redis) Flush() error {
	ctx := context.Background()
	return c.client.FlushDB(ctx).Err()
}
