package e

import "net/http"

type AppError struct {
	Msg        string
	ErrCode    int
	StatusCode int
	Root       error
}

func (e *AppError) Error() string {
	return e.Msg
}

func NewError(errCode int, httpCode int, msg string) *AppError {
	return &AppError{
		ErrCode:    errCode,
		Msg:        msg,
		StatusCode: httpCode,
	}
}

func NewError200(msg string) *AppError {
	return &AppError{
		Msg:        msg,
		StatusCode: http.StatusOK,
	}
}

func NewError400(errCode int, msg string) *AppError {
	return &AppError{
		ErrCode:    errCode,
		Msg:        msg,
		StatusCode: http.StatusBadRequest,
	}
}

func NewError401(errCode int, msg string) *AppError {
	return &AppError{
		ErrCode:    errCode,
		Msg:        msg,
		StatusCode: http.StatusUnauthorized,
	}
}

func NewError403(errCode int, msg string) *AppError {
	return &AppError{
		ErrCode:    errCode,
		Msg:        msg,
		StatusCode: http.StatusForbidden,
	}
}

func NewError404(errCode int, msg string) *AppError {
	return &AppError{
		ErrCode:    errCode,
		Msg:        msg,
		StatusCode: http.StatusNotFound,
	}
}

func NewError500(errCode int, msg string) *AppError {
	return &AppError{
		ErrCode:    errCode,
		Msg:        msg,
		StatusCode: http.StatusInternalServerError,
	}
}

func NewError502(errCode int, msg string) *AppError {
	return &AppError{
		ErrCode:    errCode,
		Msg:        msg,
		StatusCode: http.StatusBadGateway,
	}
}

func NewRootError(errCode int, httpCode int, msg string, root error) *AppError {
	return &AppError{
		ErrCode:    errCode,
		Msg:        msg,
		Root:       root,
		StatusCode: httpCode,
	}
}

func NewRootError400(errCode int, msg string, root error) *AppError {
	return &AppError{
		ErrCode:    errCode,
		Msg:        msg,
		StatusCode: http.StatusBadRequest,
		Root:       root,
	}
}

func NewRootError403(errCode int, msg string, root error) *AppError {
	return &AppError{
		ErrCode:    errCode,
		Msg:        msg,
		StatusCode: http.StatusForbidden,
		Root:       root,
	}
}

func NewRootError404(errCode int, msg string, root error) *AppError {
	return &AppError{
		ErrCode:    errCode,
		Msg:        msg,
		StatusCode: http.StatusNotFound,
		Root:       root,
	}
}

func NewRootError500(errCode int, msg string, root error) *AppError {
	return &AppError{
		ErrCode:    errCode,
		Msg:        msg,
		StatusCode: http.StatusInternalServerError,
		Root:       root,
	}
}

func NewRootError504(errCode int, msg string, root error) *AppError {
	return &AppError{
		ErrCode:    errCode,
		Msg:        msg,
		StatusCode: http.StatusBadGateway,
		Root:       root,
	}
}
