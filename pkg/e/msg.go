package e

var MsgFlags = map[int]string{
	// HTTP Status
	Success:       "Ok",
	Created:       "Ok",
	Error:         "Internal server errors",
	InvalidParams: "Invalid parameters",
	Unauthorized:  "Unauthorized",
	Forbidden:     "Forbidden",
	NotFound:      "Not found",

	CacheDeleteAllFailed:   "Internal server error while deleting all caches",
	CacheDeleteByKeyFailed: "Internal server error while deleting cache by key",

	AuthInvalidApiKey: "Invalid API key",

	WalletFindOneFailed:       "An internal error occurred when finding the wallet",
	WalletNotFound:            "Wallet not found",
	WalletFindManyFailed:      "An internal error occurred when finding the wallets",
	WalletCreateFailed:        "An internal error occurred when creating the wallet",
	WalletUpdateFailed:        "An internal error occurred when updating the wallet",
	WalletDeleteFailed:        "An internal error occurred when deleting the wallet",
	WalletUserAlreadyHave:     "User already have a wallet",
	WalletGetGasSponsorFailed: "An internal error occurred when getting the wallet sponsor gas",

	TransactionInsufficientGasFee:  "Insufficient gas fee",
	TransactionInsufficientBalance: "Insufficient balance",
	TransactionCreatePermitFailed:  "Failed to create permit",
	TransactionSignatureExpired:    "Signature expired",
}

// GetMsg get error information based on Code
func GetMsg(code int) string {
	msg, ok := MsgFlags[code]
	if ok {
		return msg
	}

	return MsgFlags[Error]
}
