package util

import (
	"crypto/ed25519"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"github.com/aurora-is-near/near-api-go/utils"
	"github.com/btcsuite/btcd/btcutil/base58"
	"github.com/cosmos/go-bip39"
	"github.com/shopspring/decimal"
	"io"
	"math/big"
	"strings"
)

const (
	Ed25519Prefix     = "ed25519:"
	NearNominationExp = 24
)

func GenerateSecret(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := io.ReadFull(rand.Reader, bytes); err != nil {
		return "", err
	}

	return base64.URLEncoding.EncodeToString(bytes), nil
}

func GenerateMnemonic() (string, error) {
	entropy, err := bip39.NewEntropy(128)
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("failed to generate mnemonic: %w", err)
	}

	mnemonic, err := bip39.NewMnemonic(entropy)
	if err != nil {
		return "", fmt.Errorf("failed to generate mnemonic: %w", err)
	}

	return mnemonic, nil
}

// GenerateSeedBuffer creates a seed buffer from the seed phrase and password
func GenerateSeedBuffer(seedPhrase, secret string) []byte {
	// Use bip39 to generate a seed with optional password
	return bip39.NewSeed(seedPhrase, secret)
}

// Ed25519PublicKeyFromString derives an ed25519 public key from its base58 string representation prefixed with 'ed25519:'.
func Ed25519PublicKeyFromString(ed25519PublicKey string) (ed25519.PublicKey, error) {
	if !strings.HasPrefix(ed25519PublicKey, Ed25519Prefix) {
		return nil, fmt.Errorf("'%s' is not an Ed25519 key", ed25519PublicKey)
	}
	keyBytes := base58.Decode(strings.TrimPrefix(ed25519PublicKey, Ed25519Prefix))
	if len(keyBytes) != 32 {
		return nil, fmt.Errorf("unexpected byte length for public key '%s'", ed25519PublicKey)
	}
	return keyBytes, nil
}

// Ed25519PrivateKeyFromString derives an ed25519 private key from its base58 string representation prefixed with 'ed25519:'.
func Ed25519PrivateKeyFromString(ed25519PrivateKey string) (ed25519.PrivateKey, error) {
	if !strings.HasPrefix(ed25519PrivateKey, Ed25519Prefix) {
		return nil, fmt.Errorf("'%s' is not an Ed25519 key", ed25519PrivateKey)
	}
	keyBytes := base58.Decode(strings.TrimPrefix(ed25519PrivateKey, Ed25519Prefix))
	if len(keyBytes) != 64 {
		return nil, fmt.Errorf("unexpected byte length for private key '%s'", ed25519PrivateKey)
	}
	return keyBytes, nil
}

func ParseNearAmountAsBigInt(amount string) (*big.Int, error) {
	return utils.ParseNearAmountAsBigInt(amount)
}

// ParseNearAmount2Yocto parses NEAR amount to yoctoNEAR amount
func ParseNearAmount2Yocto(amount decimal.Decimal) decimal.Decimal {
	multiplier := decimal.New(1, int32(utils.NearNominationExp))
	return amount.Mul(multiplier)
}

// ParseNearYocto2Amount parses yoctoNEAR amount to NEAR amount
func ParseNearYocto2Amount(amount decimal.Decimal) decimal.Decimal {
	divisor := decimal.New(1, int32(utils.NearNominationExp))
	return amount.Div(divisor)
}

func ParseRawValue2ReadableAmount(amount decimal.Decimal, decimals uint8) decimal.Decimal {
	divisor := decimal.New(1, int32(decimals))
	return amount.Div(divisor)
}

func ParseReadableAmount2RawValue(amount decimal.Decimal, decimals uint8) decimal.Decimal {
	multiplier := decimal.New(1, int32(decimals))
	return amount.Mul(multiplier)
}
