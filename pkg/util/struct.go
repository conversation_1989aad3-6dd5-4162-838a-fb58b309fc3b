package util

import (
	"encoding/json"
	"fmt"
)

func MapToStruct[T any](m map[string]interface{}) (T, error) {
	var result T

	jsonBytes, err := json.Marshal(m)
	if err != nil {
		return result, fmt.<PERSON><PERSON>rf("error marshaling map to bytes: %w", err)
	}

	if err = json.Unmarshal(jsonBytes, &result); err != nil {
		return result, fmt.Errorf("error unmarshaling bytes to struct: %w", err)
	}

	return result, nil
}

func StructToMap[T any](obj T) (map[string]interface{}, error) {
	m := map[string]interface{}{}

	jsonBytes, err := json.<PERSON>(obj)
	if err != nil {
		return nil, fmt.Errorf("error marshaling struct to bytes: %w", err)
	}

	if err = json.Unmarshal(jsonBytes, &m); err != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("error unmarshaling bytes to map: %w", err)
	}

	return m, nil
}
