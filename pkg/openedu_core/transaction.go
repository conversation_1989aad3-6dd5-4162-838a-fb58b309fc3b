package openedu_core

import (
	"encoding/json"
	"fmt"
	"openedu-blockchain/pkg/queue/producer"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/pkg/util"
)

const (
	TransactionSyncQueueName = "transaction_sync_queue"
)

type TransactionService struct {
	producer producer.Producer
}

func NewTransactionService() (*TransactionService, error) {
	p, err := producer.NewProducer(producer.RabbitMQ)
	if err != nil {
		return nil, fmt.Errorf("failed to create producer: %v", err)
	}

	return &TransactionService{
		producer: p,
	}, nil
}

func (s *TransactionService) Sync(corrID *string, routingKey *string, req *TransactionSyncRequest) error {
	bytes, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("%w: failed to marshal request: %s", ErrMakeRequest, err)
	}

	replyTo := ""
	queueName := setting.RabbitMQSetting.Prefix + TransactionSyncQueueName
	if routingKey != nil {
		queueName = *routingKey
		replyTo = *routingKey
	}
	if err = s.producer.Publish(
		queueName,
		&producer.Message{
			ID:            util.GenerateId(),
			Body:          bytes,
			ContentType:   "application/json",
			CorrelationID: corrID,
			ReplyTo:       replyTo,
		},
	); err != nil {
		return fmt.Errorf("%w: failed to send amqp publishing request: %s", ErrMakeRequest, err)
	}
	return nil
}
