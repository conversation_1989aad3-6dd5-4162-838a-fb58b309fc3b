package openedu_core

import (
	"github.com/shopspring/decimal"
)

type TransactionStatus string

type RetrieveWalletDetailsType string

type BlockchainNetwork string

type Currency string

const (
	TxnStatusPending TransactionStatus = "pending"
	TxnStatusSuccess TransactionStatus = "success"
	TxnStatusFailed  TransactionStatus = "failed"

	BlockchainNetworkNEAR BlockchainNetwork = "near"

	CryptoCurrencyNEAR    Currency = "NEAR"
	CryptoCurrencyUSDT    Currency = "USDT"
	CryptoCurrencyUSDC    Currency = "USDC"
	CryptoCurrencyOpenEdu Currency = "OPENEDU"
)

type SyncWalletRequest struct {
	ID           string          `json:"id"`
	UserID       string          `json:"user_id"`
	Address      string          `json:"address"`
	PublicKey    string          `json:"public_key"`
	Type         string          `json:"type"`
	Status       string          `json:"status"`
	Balance      decimal.Decimal `json:"balance"`
	CoreWalletID string          `json:"core_wallet_id"`
}

type TransactionSyncRequest struct {
	CoreTxIDs      []string          `json:"core_tx_ids"`
	NftTokenID     string            `json:"nft_token_id"`
	BlockchainTxID string            `json:"blockchain_tx_id"`
	TxHash         string            `json:"tx_hash"`
	Status         TransactionStatus `json:"status"`
	ErrorCode      int               `json:"error_code"`
	DecimalAmount  *decimal.Decimal  `json:"decimal_amount"`
}

type RetrieveWalletDetailsRequest struct {
	Type RetrieveWalletDetailsType `json:"type"`
	Data interface{}               `json:"data"`
}

type RetrieveWalletEarningsRequest struct {
	UserID   string                   `json:"user_id"`
	Earnings []*WalletEarningsRequest `json:"earnings"`
}

type WalletEarningsRequest struct {
	BlockchainWalletID string            `json:"blockchain_wallet_id"`
	CoreWalletID       string            `json:"core_wallet_id"`
	Address            string            `json:"address"`
	Network            BlockchainNetwork `json:"network"`
	Currency           Currency          `json:"currency"`
	TokenID            string            `json:"token_id"`
	Amount             decimal.Decimal   `json:"amount"`
}
