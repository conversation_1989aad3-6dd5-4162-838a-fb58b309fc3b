package openedu_eth_sdk

import (
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"math/big"
)

type ERC20TokenContract interface {
	Allowance(opts *bind.CallOpts, _owner common.Address, _spender common.Address) (*big.Int, error)
	Approve(opts *bind.TransactOpts, _spender common.Address, _value *big.Int) (*types.Transaction, error)
	BalanceOf(opts *bind.CallOpts, account common.Address) (*big.Int, error)
	Decimals(opts *bind.CallOpts) (uint8, error)
	Name(opts *bind.CallOpts) (string, error)
	Nonces(opts *bind.CallOpts, owner common.Address) (*big.Int, error)
	Symbol(opts *bind.CallOpts) (string, error)
	TotalSupply(opts *bind.CallOpts) (*big.Int, error)
	Transfer(opts *bind.TransactOpts, _to common.Address, _value *big.Int) (*types.Transaction, error)
	TransferFrom(opts *bind.TransactOpts, _from common.Address, _to common.Address, _value *big.Int) (*types.Transaction, error)
}
