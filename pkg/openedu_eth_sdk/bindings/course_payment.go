// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package bindings

import (
	"errors"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
)

// Reference imports to suppress errors if they are not otherwise used.
var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

// CoursePaymentMetaData contains all meta data concerning the CoursePayment contract.
var CoursePaymentMetaData = &bind.MetaData{
	ABI: "[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"initialOwner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"feeReceiver\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"CoursePayment__AlreadyClaimed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CoursePayment__AlreadyPaid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CoursePayment__InvalidPaymentId\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CoursePayment__InvalidProof\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CoursePayment__InvalidSignature\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CoursePayment__TokenNotAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidShortString\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"str\",\"type\":\"string\"}],\"name\":\"StringTooLong\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"courseId\",\"type\":\"string\"}],\"name\":\"CoursePaid\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"courseId\",\"type\":\"string\"}],\"name\":\"CoursePaidWithPermit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"EIP712DomainChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferStarted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"paymentId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"PaymentClaimed\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"acceptOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"addAllowedToken\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newFeeReceiver\",\"type\":\"address\"}],\"name\":\"changeFeeReceiver\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newVault\",\"type\":\"address\"}],\"name\":\"changeVault\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"eip712Domain\",\"outputs\":[{\"internalType\":\"bytes1\",\"name\":\"fields\",\"type\":\"bytes1\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"version\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"chainId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"verifyingContract\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"salt\",\"type\":\"bytes32\"},{\"internalType\":\"uint256[]\",\"name\":\"extensions\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"courseId\",\"type\":\"string\"}],\"name\":\"isPaid\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"courseId\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"fee\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"payWithPermit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pendingOwner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"removeAllowedToken\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}]",
}

// CoursePaymentABI is the input ABI used to generate the binding from.
// Deprecated: Use CoursePaymentMetaData.ABI instead.
var CoursePaymentABI = CoursePaymentMetaData.ABI

// CoursePayment is an auto generated Go binding around an Ethereum contract.
type CoursePayment struct {
	CoursePaymentCaller     // Read-only binding to the contract
	CoursePaymentTransactor // Write-only binding to the contract
	CoursePaymentFilterer   // Log filterer for contract events
}

// CoursePaymentCaller is an auto generated read-only Go binding around an Ethereum contract.
type CoursePaymentCaller struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// CoursePaymentTransactor is an auto generated write-only Go binding around an Ethereum contract.
type CoursePaymentTransactor struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// CoursePaymentFilterer is an auto generated log filtering Go binding around an Ethereum contract events.
type CoursePaymentFilterer struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// CoursePaymentSession is an auto generated Go binding around an Ethereum contract,
// with pre-set call and transact options.
type CoursePaymentSession struct {
	Contract     *CoursePayment    // Generic contract binding to set the session for
	CallOpts     bind.CallOpts     // Call options to use throughout this session
	TransactOpts bind.TransactOpts // Transaction auth options to use throughout this session
}

// CoursePaymentCallerSession is an auto generated read-only Go binding around an Ethereum contract,
// with pre-set call options.
type CoursePaymentCallerSession struct {
	Contract *CoursePaymentCaller // Generic contract caller binding to set the session for
	CallOpts bind.CallOpts        // Call options to use throughout this session
}

// CoursePaymentTransactorSession is an auto generated write-only Go binding around an Ethereum contract,
// with pre-set transact options.
type CoursePaymentTransactorSession struct {
	Contract     *CoursePaymentTransactor // Generic contract transactor binding to set the session for
	TransactOpts bind.TransactOpts        // Transaction auth options to use throughout this session
}

// CoursePaymentRaw is an auto generated low-level Go binding around an Ethereum contract.
type CoursePaymentRaw struct {
	Contract *CoursePayment // Generic contract binding to access the raw methods on
}

// CoursePaymentCallerRaw is an auto generated low-level read-only Go binding around an Ethereum contract.
type CoursePaymentCallerRaw struct {
	Contract *CoursePaymentCaller // Generic read-only contract binding to access the raw methods on
}

// CoursePaymentTransactorRaw is an auto generated low-level write-only Go binding around an Ethereum contract.
type CoursePaymentTransactorRaw struct {
	Contract *CoursePaymentTransactor // Generic write-only contract binding to access the raw methods on
}

// NewCoursePayment creates a new instance of CoursePayment, bound to a specific deployed contract.
func NewCoursePayment(address common.Address, backend bind.ContractBackend) (*CoursePayment, error) {
	contract, err := bindCoursePayment(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &CoursePayment{CoursePaymentCaller: CoursePaymentCaller{contract: contract}, CoursePaymentTransactor: CoursePaymentTransactor{contract: contract}, CoursePaymentFilterer: CoursePaymentFilterer{contract: contract}}, nil
}

// NewCoursePaymentCaller creates a new read-only instance of CoursePayment, bound to a specific deployed contract.
func NewCoursePaymentCaller(address common.Address, caller bind.ContractCaller) (*CoursePaymentCaller, error) {
	contract, err := bindCoursePayment(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &CoursePaymentCaller{contract: contract}, nil
}

// NewCoursePaymentTransactor creates a new write-only instance of CoursePayment, bound to a specific deployed contract.
func NewCoursePaymentTransactor(address common.Address, transactor bind.ContractTransactor) (*CoursePaymentTransactor, error) {
	contract, err := bindCoursePayment(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &CoursePaymentTransactor{contract: contract}, nil
}

// NewCoursePaymentFilterer creates a new log filterer instance of CoursePayment, bound to a specific deployed contract.
func NewCoursePaymentFilterer(address common.Address, filterer bind.ContractFilterer) (*CoursePaymentFilterer, error) {
	contract, err := bindCoursePayment(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &CoursePaymentFilterer{contract: contract}, nil
}

// bindCoursePayment binds a generic wrapper to an already deployed contract.
func bindCoursePayment(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := CoursePaymentMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_CoursePayment *CoursePaymentRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _CoursePayment.Contract.CoursePaymentCaller.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_CoursePayment *CoursePaymentRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CoursePayment.Contract.CoursePaymentTransactor.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_CoursePayment *CoursePaymentRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _CoursePayment.Contract.CoursePaymentTransactor.contract.Transact(opts, method, params...)
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_CoursePayment *CoursePaymentCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _CoursePayment.Contract.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_CoursePayment *CoursePaymentTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CoursePayment.Contract.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_CoursePayment *CoursePaymentTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _CoursePayment.Contract.contract.Transact(opts, method, params...)
}

// Eip712Domain is a free data retrieval call binding the contract method 0x84b0196e.
//
// Solidity: function eip712Domain() view returns(bytes1 fields, string name, string version, uint256 chainId, address verifyingContract, bytes32 salt, uint256[] extensions)
func (_CoursePayment *CoursePaymentCaller) Eip712Domain(opts *bind.CallOpts) (struct {
	Fields            [1]byte
	Name              string
	Version           string
	ChainId           *big.Int
	VerifyingContract common.Address
	Salt              [32]byte
	Extensions        []*big.Int
}, error) {
	var out []interface{}
	err := _CoursePayment.contract.Call(opts, &out, "eip712Domain")

	outstruct := new(struct {
		Fields            [1]byte
		Name              string
		Version           string
		ChainId           *big.Int
		VerifyingContract common.Address
		Salt              [32]byte
		Extensions        []*big.Int
	})
	if err != nil {
		return *outstruct, err
	}

	outstruct.Fields = *abi.ConvertType(out[0], new([1]byte)).(*[1]byte)
	outstruct.Name = *abi.ConvertType(out[1], new(string)).(*string)
	outstruct.Version = *abi.ConvertType(out[2], new(string)).(*string)
	outstruct.ChainId = *abi.ConvertType(out[3], new(*big.Int)).(**big.Int)
	outstruct.VerifyingContract = *abi.ConvertType(out[4], new(common.Address)).(*common.Address)
	outstruct.Salt = *abi.ConvertType(out[5], new([32]byte)).(*[32]byte)
	outstruct.Extensions = *abi.ConvertType(out[6], new([]*big.Int)).(*[]*big.Int)

	return *outstruct, err

}

// Eip712Domain is a free data retrieval call binding the contract method 0x84b0196e.
//
// Solidity: function eip712Domain() view returns(bytes1 fields, string name, string version, uint256 chainId, address verifyingContract, bytes32 salt, uint256[] extensions)
func (_CoursePayment *CoursePaymentSession) Eip712Domain() (struct {
	Fields            [1]byte
	Name              string
	Version           string
	ChainId           *big.Int
	VerifyingContract common.Address
	Salt              [32]byte
	Extensions        []*big.Int
}, error) {
	return _CoursePayment.Contract.Eip712Domain(&_CoursePayment.CallOpts)
}

// Eip712Domain is a free data retrieval call binding the contract method 0x84b0196e.
//
// Solidity: function eip712Domain() view returns(bytes1 fields, string name, string version, uint256 chainId, address verifyingContract, bytes32 salt, uint256[] extensions)
func (_CoursePayment *CoursePaymentCallerSession) Eip712Domain() (struct {
	Fields            [1]byte
	Name              string
	Version           string
	ChainId           *big.Int
	VerifyingContract common.Address
	Salt              [32]byte
	Extensions        []*big.Int
}, error) {
	return _CoursePayment.Contract.Eip712Domain(&_CoursePayment.CallOpts)
}

// IsPaid is a free data retrieval call binding the contract method 0xba61232b.
//
// Solidity: function isPaid(address user, string courseId) view returns(bool)
func (_CoursePayment *CoursePaymentCaller) IsPaid(opts *bind.CallOpts, user common.Address, courseId string) (bool, error) {
	var out []interface{}
	err := _CoursePayment.contract.Call(opts, &out, "isPaid", user, courseId)

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// IsPaid is a free data retrieval call binding the contract method 0xba61232b.
//
// Solidity: function isPaid(address user, string courseId) view returns(bool)
func (_CoursePayment *CoursePaymentSession) IsPaid(user common.Address, courseId string) (bool, error) {
	return _CoursePayment.Contract.IsPaid(&_CoursePayment.CallOpts, user, courseId)
}

// IsPaid is a free data retrieval call binding the contract method 0xba61232b.
//
// Solidity: function isPaid(address user, string courseId) view returns(bool)
func (_CoursePayment *CoursePaymentCallerSession) IsPaid(user common.Address, courseId string) (bool, error) {
	return _CoursePayment.Contract.IsPaid(&_CoursePayment.CallOpts, user, courseId)
}

// Owner is a free data retrieval call binding the contract method 0x8da5cb5b.
//
// Solidity: function owner() view returns(address)
func (_CoursePayment *CoursePaymentCaller) Owner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _CoursePayment.contract.Call(opts, &out, "owner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Owner is a free data retrieval call binding the contract method 0x8da5cb5b.
//
// Solidity: function owner() view returns(address)
func (_CoursePayment *CoursePaymentSession) Owner() (common.Address, error) {
	return _CoursePayment.Contract.Owner(&_CoursePayment.CallOpts)
}

// Owner is a free data retrieval call binding the contract method 0x8da5cb5b.
//
// Solidity: function owner() view returns(address)
func (_CoursePayment *CoursePaymentCallerSession) Owner() (common.Address, error) {
	return _CoursePayment.Contract.Owner(&_CoursePayment.CallOpts)
}

// PendingOwner is a free data retrieval call binding the contract method 0xe30c3978.
//
// Solidity: function pendingOwner() view returns(address)
func (_CoursePayment *CoursePaymentCaller) PendingOwner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _CoursePayment.contract.Call(opts, &out, "pendingOwner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// PendingOwner is a free data retrieval call binding the contract method 0xe30c3978.
//
// Solidity: function pendingOwner() view returns(address)
func (_CoursePayment *CoursePaymentSession) PendingOwner() (common.Address, error) {
	return _CoursePayment.Contract.PendingOwner(&_CoursePayment.CallOpts)
}

// PendingOwner is a free data retrieval call binding the contract method 0xe30c3978.
//
// Solidity: function pendingOwner() view returns(address)
func (_CoursePayment *CoursePaymentCallerSession) PendingOwner() (common.Address, error) {
	return _CoursePayment.Contract.PendingOwner(&_CoursePayment.CallOpts)
}

// AcceptOwnership is a paid mutator transaction binding the contract method 0x79ba5097.
//
// Solidity: function acceptOwnership() returns()
func (_CoursePayment *CoursePaymentTransactor) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CoursePayment.contract.Transact(opts, "acceptOwnership")
}

// AcceptOwnership is a paid mutator transaction binding the contract method 0x79ba5097.
//
// Solidity: function acceptOwnership() returns()
func (_CoursePayment *CoursePaymentSession) AcceptOwnership() (*types.Transaction, error) {
	return _CoursePayment.Contract.AcceptOwnership(&_CoursePayment.TransactOpts)
}

// AcceptOwnership is a paid mutator transaction binding the contract method 0x79ba5097.
//
// Solidity: function acceptOwnership() returns()
func (_CoursePayment *CoursePaymentTransactorSession) AcceptOwnership() (*types.Transaction, error) {
	return _CoursePayment.Contract.AcceptOwnership(&_CoursePayment.TransactOpts)
}

// AddAllowedToken is a paid mutator transaction binding the contract method 0x4178617f.
//
// Solidity: function addAllowedToken(address token) returns()
func (_CoursePayment *CoursePaymentTransactor) AddAllowedToken(opts *bind.TransactOpts, token common.Address) (*types.Transaction, error) {
	return _CoursePayment.contract.Transact(opts, "addAllowedToken", token)
}

// AddAllowedToken is a paid mutator transaction binding the contract method 0x4178617f.
//
// Solidity: function addAllowedToken(address token) returns()
func (_CoursePayment *CoursePaymentSession) AddAllowedToken(token common.Address) (*types.Transaction, error) {
	return _CoursePayment.Contract.AddAllowedToken(&_CoursePayment.TransactOpts, token)
}

// AddAllowedToken is a paid mutator transaction binding the contract method 0x4178617f.
//
// Solidity: function addAllowedToken(address token) returns()
func (_CoursePayment *CoursePaymentTransactorSession) AddAllowedToken(token common.Address) (*types.Transaction, error) {
	return _CoursePayment.Contract.AddAllowedToken(&_CoursePayment.TransactOpts, token)
}

// ChangeFeeReceiver is a paid mutator transaction binding the contract method 0x7c08b964.
//
// Solidity: function changeFeeReceiver(address newFeeReceiver) returns()
func (_CoursePayment *CoursePaymentTransactor) ChangeFeeReceiver(opts *bind.TransactOpts, newFeeReceiver common.Address) (*types.Transaction, error) {
	return _CoursePayment.contract.Transact(opts, "changeFeeReceiver", newFeeReceiver)
}

// ChangeFeeReceiver is a paid mutator transaction binding the contract method 0x7c08b964.
//
// Solidity: function changeFeeReceiver(address newFeeReceiver) returns()
func (_CoursePayment *CoursePaymentSession) ChangeFeeReceiver(newFeeReceiver common.Address) (*types.Transaction, error) {
	return _CoursePayment.Contract.ChangeFeeReceiver(&_CoursePayment.TransactOpts, newFeeReceiver)
}

// ChangeFeeReceiver is a paid mutator transaction binding the contract method 0x7c08b964.
//
// Solidity: function changeFeeReceiver(address newFeeReceiver) returns()
func (_CoursePayment *CoursePaymentTransactorSession) ChangeFeeReceiver(newFeeReceiver common.Address) (*types.Transaction, error) {
	return _CoursePayment.Contract.ChangeFeeReceiver(&_CoursePayment.TransactOpts, newFeeReceiver)
}

// ChangeVault is a paid mutator transaction binding the contract method 0x60e232a9.
//
// Solidity: function changeVault(address newVault) returns()
func (_CoursePayment *CoursePaymentTransactor) ChangeVault(opts *bind.TransactOpts, newVault common.Address) (*types.Transaction, error) {
	return _CoursePayment.contract.Transact(opts, "changeVault", newVault)
}

// ChangeVault is a paid mutator transaction binding the contract method 0x60e232a9.
//
// Solidity: function changeVault(address newVault) returns()
func (_CoursePayment *CoursePaymentSession) ChangeVault(newVault common.Address) (*types.Transaction, error) {
	return _CoursePayment.Contract.ChangeVault(&_CoursePayment.TransactOpts, newVault)
}

// ChangeVault is a paid mutator transaction binding the contract method 0x60e232a9.
//
// Solidity: function changeVault(address newVault) returns()
func (_CoursePayment *CoursePaymentTransactorSession) ChangeVault(newVault common.Address) (*types.Transaction, error) {
	return _CoursePayment.Contract.ChangeVault(&_CoursePayment.TransactOpts, newVault)
}

// PayWithPermit is a paid mutator transaction binding the contract method 0x86d81c24.
//
// Solidity: function payWithPermit(address token, address sender, string courseId, uint256 amount, uint256 fee, uint256 deadline, uint8 v, bytes32 r, bytes32 s) returns()
func (_CoursePayment *CoursePaymentTransactor) PayWithPermit(opts *bind.TransactOpts, token common.Address, sender common.Address, courseId string, amount *big.Int, fee *big.Int, deadline *big.Int, v uint8, r [32]byte, s [32]byte) (*types.Transaction, error) {
	return _CoursePayment.contract.Transact(opts, "payWithPermit", token, sender, courseId, amount, fee, deadline, v, r, s)
}

// PayWithPermit is a paid mutator transaction binding the contract method 0x86d81c24.
//
// Solidity: function payWithPermit(address token, address sender, string courseId, uint256 amount, uint256 fee, uint256 deadline, uint8 v, bytes32 r, bytes32 s) returns()
func (_CoursePayment *CoursePaymentSession) PayWithPermit(token common.Address, sender common.Address, courseId string, amount *big.Int, fee *big.Int, deadline *big.Int, v uint8, r [32]byte, s [32]byte) (*types.Transaction, error) {
	return _CoursePayment.Contract.PayWithPermit(&_CoursePayment.TransactOpts, token, sender, courseId, amount, fee, deadline, v, r, s)
}

// PayWithPermit is a paid mutator transaction binding the contract method 0x86d81c24.
//
// Solidity: function payWithPermit(address token, address sender, string courseId, uint256 amount, uint256 fee, uint256 deadline, uint8 v, bytes32 r, bytes32 s) returns()
func (_CoursePayment *CoursePaymentTransactorSession) PayWithPermit(token common.Address, sender common.Address, courseId string, amount *big.Int, fee *big.Int, deadline *big.Int, v uint8, r [32]byte, s [32]byte) (*types.Transaction, error) {
	return _CoursePayment.Contract.PayWithPermit(&_CoursePayment.TransactOpts, token, sender, courseId, amount, fee, deadline, v, r, s)
}

// RemoveAllowedToken is a paid mutator transaction binding the contract method 0x90469a9d.
//
// Solidity: function removeAllowedToken(address token) returns()
func (_CoursePayment *CoursePaymentTransactor) RemoveAllowedToken(opts *bind.TransactOpts, token common.Address) (*types.Transaction, error) {
	return _CoursePayment.contract.Transact(opts, "removeAllowedToken", token)
}

// RemoveAllowedToken is a paid mutator transaction binding the contract method 0x90469a9d.
//
// Solidity: function removeAllowedToken(address token) returns()
func (_CoursePayment *CoursePaymentSession) RemoveAllowedToken(token common.Address) (*types.Transaction, error) {
	return _CoursePayment.Contract.RemoveAllowedToken(&_CoursePayment.TransactOpts, token)
}

// RemoveAllowedToken is a paid mutator transaction binding the contract method 0x90469a9d.
//
// Solidity: function removeAllowedToken(address token) returns()
func (_CoursePayment *CoursePaymentTransactorSession) RemoveAllowedToken(token common.Address) (*types.Transaction, error) {
	return _CoursePayment.Contract.RemoveAllowedToken(&_CoursePayment.TransactOpts, token)
}

// RenounceOwnership is a paid mutator transaction binding the contract method 0x715018a6.
//
// Solidity: function renounceOwnership() returns()
func (_CoursePayment *CoursePaymentTransactor) RenounceOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CoursePayment.contract.Transact(opts, "renounceOwnership")
}

// RenounceOwnership is a paid mutator transaction binding the contract method 0x715018a6.
//
// Solidity: function renounceOwnership() returns()
func (_CoursePayment *CoursePaymentSession) RenounceOwnership() (*types.Transaction, error) {
	return _CoursePayment.Contract.RenounceOwnership(&_CoursePayment.TransactOpts)
}

// RenounceOwnership is a paid mutator transaction binding the contract method 0x715018a6.
//
// Solidity: function renounceOwnership() returns()
func (_CoursePayment *CoursePaymentTransactorSession) RenounceOwnership() (*types.Transaction, error) {
	return _CoursePayment.Contract.RenounceOwnership(&_CoursePayment.TransactOpts)
}

// TransferOwnership is a paid mutator transaction binding the contract method 0xf2fde38b.
//
// Solidity: function transferOwnership(address newOwner) returns()
func (_CoursePayment *CoursePaymentTransactor) TransferOwnership(opts *bind.TransactOpts, newOwner common.Address) (*types.Transaction, error) {
	return _CoursePayment.contract.Transact(opts, "transferOwnership", newOwner)
}

// TransferOwnership is a paid mutator transaction binding the contract method 0xf2fde38b.
//
// Solidity: function transferOwnership(address newOwner) returns()
func (_CoursePayment *CoursePaymentSession) TransferOwnership(newOwner common.Address) (*types.Transaction, error) {
	return _CoursePayment.Contract.TransferOwnership(&_CoursePayment.TransactOpts, newOwner)
}

// TransferOwnership is a paid mutator transaction binding the contract method 0xf2fde38b.
//
// Solidity: function transferOwnership(address newOwner) returns()
func (_CoursePayment *CoursePaymentTransactorSession) TransferOwnership(newOwner common.Address) (*types.Transaction, error) {
	return _CoursePayment.Contract.TransferOwnership(&_CoursePayment.TransactOpts, newOwner)
}

// CoursePaymentCoursePaidIterator is returned from FilterCoursePaid and is used to iterate over the raw logs and unpacked data for CoursePaid events raised by the CoursePayment contract.
type CoursePaymentCoursePaidIterator struct {
	Event *CoursePaymentCoursePaid // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *CoursePaymentCoursePaidIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CoursePaymentCoursePaid)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(CoursePaymentCoursePaid)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *CoursePaymentCoursePaidIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *CoursePaymentCoursePaidIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// CoursePaymentCoursePaid represents a CoursePaid event raised by the CoursePayment contract.
type CoursePaymentCoursePaid struct {
	User     common.Address
	CourseId string
	Raw      types.Log // Blockchain specific contextual infos
}

// FilterCoursePaid is a free log retrieval operation binding the contract event 0x77601439d026d42881789d24aa7a3cd9eedd3eae361d7fe48569b71acf5a4004.
//
// Solidity: event CoursePaid(address indexed user, string courseId)
func (_CoursePayment *CoursePaymentFilterer) FilterCoursePaid(opts *bind.FilterOpts, user []common.Address) (*CoursePaymentCoursePaidIterator, error) {

	var userRule []interface{}
	for _, userItem := range user {
		userRule = append(userRule, userItem)
	}

	logs, sub, err := _CoursePayment.contract.FilterLogs(opts, "CoursePaid", userRule)
	if err != nil {
		return nil, err
	}
	return &CoursePaymentCoursePaidIterator{contract: _CoursePayment.contract, event: "CoursePaid", logs: logs, sub: sub}, nil
}

// WatchCoursePaid is a free log subscription operation binding the contract event 0x77601439d026d42881789d24aa7a3cd9eedd3eae361d7fe48569b71acf5a4004.
//
// Solidity: event CoursePaid(address indexed user, string courseId)
func (_CoursePayment *CoursePaymentFilterer) WatchCoursePaid(opts *bind.WatchOpts, sink chan<- *CoursePaymentCoursePaid, user []common.Address) (event.Subscription, error) {

	var userRule []interface{}
	for _, userItem := range user {
		userRule = append(userRule, userItem)
	}

	logs, sub, err := _CoursePayment.contract.WatchLogs(opts, "CoursePaid", userRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(CoursePaymentCoursePaid)
				if err := _CoursePayment.contract.UnpackLog(event, "CoursePaid", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseCoursePaid is a log parse operation binding the contract event 0x77601439d026d42881789d24aa7a3cd9eedd3eae361d7fe48569b71acf5a4004.
//
// Solidity: event CoursePaid(address indexed user, string courseId)
func (_CoursePayment *CoursePaymentFilterer) ParseCoursePaid(log types.Log) (*CoursePaymentCoursePaid, error) {
	event := new(CoursePaymentCoursePaid)
	if err := _CoursePayment.contract.UnpackLog(event, "CoursePaid", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// CoursePaymentCoursePaidWithPermitIterator is returned from FilterCoursePaidWithPermit and is used to iterate over the raw logs and unpacked data for CoursePaidWithPermit events raised by the CoursePayment contract.
type CoursePaymentCoursePaidWithPermitIterator struct {
	Event *CoursePaymentCoursePaidWithPermit // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *CoursePaymentCoursePaidWithPermitIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CoursePaymentCoursePaidWithPermit)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(CoursePaymentCoursePaidWithPermit)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *CoursePaymentCoursePaidWithPermitIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *CoursePaymentCoursePaidWithPermitIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// CoursePaymentCoursePaidWithPermit represents a CoursePaidWithPermit event raised by the CoursePayment contract.
type CoursePaymentCoursePaidWithPermit struct {
	User     common.Address
	CourseId string
	Raw      types.Log // Blockchain specific contextual infos
}

// FilterCoursePaidWithPermit is a free log retrieval operation binding the contract event 0xd5360d4dc73c2e4c70950dd8ff4575146fc97e1913dafb936dccc59fb86b47c1.
//
// Solidity: event CoursePaidWithPermit(address indexed user, string courseId)
func (_CoursePayment *CoursePaymentFilterer) FilterCoursePaidWithPermit(opts *bind.FilterOpts, user []common.Address) (*CoursePaymentCoursePaidWithPermitIterator, error) {

	var userRule []interface{}
	for _, userItem := range user {
		userRule = append(userRule, userItem)
	}

	logs, sub, err := _CoursePayment.contract.FilterLogs(opts, "CoursePaidWithPermit", userRule)
	if err != nil {
		return nil, err
	}
	return &CoursePaymentCoursePaidWithPermitIterator{contract: _CoursePayment.contract, event: "CoursePaidWithPermit", logs: logs, sub: sub}, nil
}

// WatchCoursePaidWithPermit is a free log subscription operation binding the contract event 0xd5360d4dc73c2e4c70950dd8ff4575146fc97e1913dafb936dccc59fb86b47c1.
//
// Solidity: event CoursePaidWithPermit(address indexed user, string courseId)
func (_CoursePayment *CoursePaymentFilterer) WatchCoursePaidWithPermit(opts *bind.WatchOpts, sink chan<- *CoursePaymentCoursePaidWithPermit, user []common.Address) (event.Subscription, error) {

	var userRule []interface{}
	for _, userItem := range user {
		userRule = append(userRule, userItem)
	}

	logs, sub, err := _CoursePayment.contract.WatchLogs(opts, "CoursePaidWithPermit", userRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(CoursePaymentCoursePaidWithPermit)
				if err := _CoursePayment.contract.UnpackLog(event, "CoursePaidWithPermit", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseCoursePaidWithPermit is a log parse operation binding the contract event 0xd5360d4dc73c2e4c70950dd8ff4575146fc97e1913dafb936dccc59fb86b47c1.
//
// Solidity: event CoursePaidWithPermit(address indexed user, string courseId)
func (_CoursePayment *CoursePaymentFilterer) ParseCoursePaidWithPermit(log types.Log) (*CoursePaymentCoursePaidWithPermit, error) {
	event := new(CoursePaymentCoursePaidWithPermit)
	if err := _CoursePayment.contract.UnpackLog(event, "CoursePaidWithPermit", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// CoursePaymentEIP712DomainChangedIterator is returned from FilterEIP712DomainChanged and is used to iterate over the raw logs and unpacked data for EIP712DomainChanged events raised by the CoursePayment contract.
type CoursePaymentEIP712DomainChangedIterator struct {
	Event *CoursePaymentEIP712DomainChanged // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *CoursePaymentEIP712DomainChangedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CoursePaymentEIP712DomainChanged)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(CoursePaymentEIP712DomainChanged)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *CoursePaymentEIP712DomainChangedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *CoursePaymentEIP712DomainChangedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// CoursePaymentEIP712DomainChanged represents a EIP712DomainChanged event raised by the CoursePayment contract.
type CoursePaymentEIP712DomainChanged struct {
	Raw types.Log // Blockchain specific contextual infos
}

// FilterEIP712DomainChanged is a free log retrieval operation binding the contract event 0x0a6387c9ea3628b88a633bb4f3b151770f70085117a15f9bf3787cda53f13d31.
//
// Solidity: event EIP712DomainChanged()
func (_CoursePayment *CoursePaymentFilterer) FilterEIP712DomainChanged(opts *bind.FilterOpts) (*CoursePaymentEIP712DomainChangedIterator, error) {

	logs, sub, err := _CoursePayment.contract.FilterLogs(opts, "EIP712DomainChanged")
	if err != nil {
		return nil, err
	}
	return &CoursePaymentEIP712DomainChangedIterator{contract: _CoursePayment.contract, event: "EIP712DomainChanged", logs: logs, sub: sub}, nil
}

// WatchEIP712DomainChanged is a free log subscription operation binding the contract event 0x0a6387c9ea3628b88a633bb4f3b151770f70085117a15f9bf3787cda53f13d31.
//
// Solidity: event EIP712DomainChanged()
func (_CoursePayment *CoursePaymentFilterer) WatchEIP712DomainChanged(opts *bind.WatchOpts, sink chan<- *CoursePaymentEIP712DomainChanged) (event.Subscription, error) {

	logs, sub, err := _CoursePayment.contract.WatchLogs(opts, "EIP712DomainChanged")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(CoursePaymentEIP712DomainChanged)
				if err := _CoursePayment.contract.UnpackLog(event, "EIP712DomainChanged", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseEIP712DomainChanged is a log parse operation binding the contract event 0x0a6387c9ea3628b88a633bb4f3b151770f70085117a15f9bf3787cda53f13d31.
//
// Solidity: event EIP712DomainChanged()
func (_CoursePayment *CoursePaymentFilterer) ParseEIP712DomainChanged(log types.Log) (*CoursePaymentEIP712DomainChanged, error) {
	event := new(CoursePaymentEIP712DomainChanged)
	if err := _CoursePayment.contract.UnpackLog(event, "EIP712DomainChanged", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// CoursePaymentOwnershipTransferStartedIterator is returned from FilterOwnershipTransferStarted and is used to iterate over the raw logs and unpacked data for OwnershipTransferStarted events raised by the CoursePayment contract.
type CoursePaymentOwnershipTransferStartedIterator struct {
	Event *CoursePaymentOwnershipTransferStarted // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *CoursePaymentOwnershipTransferStartedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CoursePaymentOwnershipTransferStarted)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(CoursePaymentOwnershipTransferStarted)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *CoursePaymentOwnershipTransferStartedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *CoursePaymentOwnershipTransferStartedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// CoursePaymentOwnershipTransferStarted represents a OwnershipTransferStarted event raised by the CoursePayment contract.
type CoursePaymentOwnershipTransferStarted struct {
	PreviousOwner common.Address
	NewOwner      common.Address
	Raw           types.Log // Blockchain specific contextual infos
}

// FilterOwnershipTransferStarted is a free log retrieval operation binding the contract event 0x38d16b8cac22d99fc7c124b9cd0de2d3fa1faef420bfe791d8c362d765e22700.
//
// Solidity: event OwnershipTransferStarted(address indexed previousOwner, address indexed newOwner)
func (_CoursePayment *CoursePaymentFilterer) FilterOwnershipTransferStarted(opts *bind.FilterOpts, previousOwner []common.Address, newOwner []common.Address) (*CoursePaymentOwnershipTransferStartedIterator, error) {

	var previousOwnerRule []interface{}
	for _, previousOwnerItem := range previousOwner {
		previousOwnerRule = append(previousOwnerRule, previousOwnerItem)
	}
	var newOwnerRule []interface{}
	for _, newOwnerItem := range newOwner {
		newOwnerRule = append(newOwnerRule, newOwnerItem)
	}

	logs, sub, err := _CoursePayment.contract.FilterLogs(opts, "OwnershipTransferStarted", previousOwnerRule, newOwnerRule)
	if err != nil {
		return nil, err
	}
	return &CoursePaymentOwnershipTransferStartedIterator{contract: _CoursePayment.contract, event: "OwnershipTransferStarted", logs: logs, sub: sub}, nil
}

// WatchOwnershipTransferStarted is a free log subscription operation binding the contract event 0x38d16b8cac22d99fc7c124b9cd0de2d3fa1faef420bfe791d8c362d765e22700.
//
// Solidity: event OwnershipTransferStarted(address indexed previousOwner, address indexed newOwner)
func (_CoursePayment *CoursePaymentFilterer) WatchOwnershipTransferStarted(opts *bind.WatchOpts, sink chan<- *CoursePaymentOwnershipTransferStarted, previousOwner []common.Address, newOwner []common.Address) (event.Subscription, error) {

	var previousOwnerRule []interface{}
	for _, previousOwnerItem := range previousOwner {
		previousOwnerRule = append(previousOwnerRule, previousOwnerItem)
	}
	var newOwnerRule []interface{}
	for _, newOwnerItem := range newOwner {
		newOwnerRule = append(newOwnerRule, newOwnerItem)
	}

	logs, sub, err := _CoursePayment.contract.WatchLogs(opts, "OwnershipTransferStarted", previousOwnerRule, newOwnerRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(CoursePaymentOwnershipTransferStarted)
				if err := _CoursePayment.contract.UnpackLog(event, "OwnershipTransferStarted", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseOwnershipTransferStarted is a log parse operation binding the contract event 0x38d16b8cac22d99fc7c124b9cd0de2d3fa1faef420bfe791d8c362d765e22700.
//
// Solidity: event OwnershipTransferStarted(address indexed previousOwner, address indexed newOwner)
func (_CoursePayment *CoursePaymentFilterer) ParseOwnershipTransferStarted(log types.Log) (*CoursePaymentOwnershipTransferStarted, error) {
	event := new(CoursePaymentOwnershipTransferStarted)
	if err := _CoursePayment.contract.UnpackLog(event, "OwnershipTransferStarted", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// CoursePaymentOwnershipTransferredIterator is returned from FilterOwnershipTransferred and is used to iterate over the raw logs and unpacked data for OwnershipTransferred events raised by the CoursePayment contract.
type CoursePaymentOwnershipTransferredIterator struct {
	Event *CoursePaymentOwnershipTransferred // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *CoursePaymentOwnershipTransferredIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CoursePaymentOwnershipTransferred)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(CoursePaymentOwnershipTransferred)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *CoursePaymentOwnershipTransferredIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *CoursePaymentOwnershipTransferredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// CoursePaymentOwnershipTransferred represents a OwnershipTransferred event raised by the CoursePayment contract.
type CoursePaymentOwnershipTransferred struct {
	PreviousOwner common.Address
	NewOwner      common.Address
	Raw           types.Log // Blockchain specific contextual infos
}

// FilterOwnershipTransferred is a free log retrieval operation binding the contract event 0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0.
//
// Solidity: event OwnershipTransferred(address indexed previousOwner, address indexed newOwner)
func (_CoursePayment *CoursePaymentFilterer) FilterOwnershipTransferred(opts *bind.FilterOpts, previousOwner []common.Address, newOwner []common.Address) (*CoursePaymentOwnershipTransferredIterator, error) {

	var previousOwnerRule []interface{}
	for _, previousOwnerItem := range previousOwner {
		previousOwnerRule = append(previousOwnerRule, previousOwnerItem)
	}
	var newOwnerRule []interface{}
	for _, newOwnerItem := range newOwner {
		newOwnerRule = append(newOwnerRule, newOwnerItem)
	}

	logs, sub, err := _CoursePayment.contract.FilterLogs(opts, "OwnershipTransferred", previousOwnerRule, newOwnerRule)
	if err != nil {
		return nil, err
	}
	return &CoursePaymentOwnershipTransferredIterator{contract: _CoursePayment.contract, event: "OwnershipTransferred", logs: logs, sub: sub}, nil
}

// WatchOwnershipTransferred is a free log subscription operation binding the contract event 0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0.
//
// Solidity: event OwnershipTransferred(address indexed previousOwner, address indexed newOwner)
func (_CoursePayment *CoursePaymentFilterer) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *CoursePaymentOwnershipTransferred, previousOwner []common.Address, newOwner []common.Address) (event.Subscription, error) {

	var previousOwnerRule []interface{}
	for _, previousOwnerItem := range previousOwner {
		previousOwnerRule = append(previousOwnerRule, previousOwnerItem)
	}
	var newOwnerRule []interface{}
	for _, newOwnerItem := range newOwner {
		newOwnerRule = append(newOwnerRule, newOwnerItem)
	}

	logs, sub, err := _CoursePayment.contract.WatchLogs(opts, "OwnershipTransferred", previousOwnerRule, newOwnerRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(CoursePaymentOwnershipTransferred)
				if err := _CoursePayment.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseOwnershipTransferred is a log parse operation binding the contract event 0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0.
//
// Solidity: event OwnershipTransferred(address indexed previousOwner, address indexed newOwner)
func (_CoursePayment *CoursePaymentFilterer) ParseOwnershipTransferred(log types.Log) (*CoursePaymentOwnershipTransferred, error) {
	event := new(CoursePaymentOwnershipTransferred)
	if err := _CoursePayment.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// CoursePaymentPaymentClaimedIterator is returned from FilterPaymentClaimed and is used to iterate over the raw logs and unpacked data for PaymentClaimed events raised by the CoursePayment contract.
type CoursePaymentPaymentClaimedIterator struct {
	Event *CoursePaymentPaymentClaimed // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *CoursePaymentPaymentClaimedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CoursePaymentPaymentClaimed)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(CoursePaymentPaymentClaimed)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *CoursePaymentPaymentClaimedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *CoursePaymentPaymentClaimedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// CoursePaymentPaymentClaimed represents a PaymentClaimed event raised by the CoursePayment contract.
type CoursePaymentPaymentClaimed struct {
	PaymentId *big.Int
	Account   common.Address
	Amount    *big.Int
	Raw       types.Log // Blockchain specific contextual infos
}

// FilterPaymentClaimed is a free log retrieval operation binding the contract event 0xdd74eb9cba06f514be98342b0ed0290146aee9feaf27e8a7ddcb151bb43dac86.
//
// Solidity: event PaymentClaimed(uint256 indexed paymentId, address indexed account, uint256 amount)
func (_CoursePayment *CoursePaymentFilterer) FilterPaymentClaimed(opts *bind.FilterOpts, paymentId []*big.Int, account []common.Address) (*CoursePaymentPaymentClaimedIterator, error) {

	var paymentIdRule []interface{}
	for _, paymentIdItem := range paymentId {
		paymentIdRule = append(paymentIdRule, paymentIdItem)
	}
	var accountRule []interface{}
	for _, accountItem := range account {
		accountRule = append(accountRule, accountItem)
	}

	logs, sub, err := _CoursePayment.contract.FilterLogs(opts, "PaymentClaimed", paymentIdRule, accountRule)
	if err != nil {
		return nil, err
	}
	return &CoursePaymentPaymentClaimedIterator{contract: _CoursePayment.contract, event: "PaymentClaimed", logs: logs, sub: sub}, nil
}

// WatchPaymentClaimed is a free log subscription operation binding the contract event 0xdd74eb9cba06f514be98342b0ed0290146aee9feaf27e8a7ddcb151bb43dac86.
//
// Solidity: event PaymentClaimed(uint256 indexed paymentId, address indexed account, uint256 amount)
func (_CoursePayment *CoursePaymentFilterer) WatchPaymentClaimed(opts *bind.WatchOpts, sink chan<- *CoursePaymentPaymentClaimed, paymentId []*big.Int, account []common.Address) (event.Subscription, error) {

	var paymentIdRule []interface{}
	for _, paymentIdItem := range paymentId {
		paymentIdRule = append(paymentIdRule, paymentIdItem)
	}
	var accountRule []interface{}
	for _, accountItem := range account {
		accountRule = append(accountRule, accountItem)
	}

	logs, sub, err := _CoursePayment.contract.WatchLogs(opts, "PaymentClaimed", paymentIdRule, accountRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(CoursePaymentPaymentClaimed)
				if err := _CoursePayment.contract.UnpackLog(event, "PaymentClaimed", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParsePaymentClaimed is a log parse operation binding the contract event 0xdd74eb9cba06f514be98342b0ed0290146aee9feaf27e8a7ddcb151bb43dac86.
//
// Solidity: event PaymentClaimed(uint256 indexed paymentId, address indexed account, uint256 amount)
func (_CoursePayment *CoursePaymentFilterer) ParsePaymentClaimed(log types.Log) (*CoursePaymentPaymentClaimed, error) {
	event := new(CoursePaymentPaymentClaimed)
	if err := _CoursePayment.contract.UnpackLog(event, "PaymentClaimed", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}
