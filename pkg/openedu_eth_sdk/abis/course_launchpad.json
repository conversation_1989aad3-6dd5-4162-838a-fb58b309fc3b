[{"inputs": [{"internalType": "address", "name": "initialOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "uint256", "name": "provided", "type": "uint256"}, {"internalType": "uint256", "name": "required", "type": "uint256"}], "name": "InvalidAmount", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "provided", "type": "uint256"}, {"internalType": "uint256", "name": "required", "type": "uint256"}], "name": "InvalidFundingDuration", "type": "error"}, {"inputs": [{"internalType": "enum ICourseLaunchpad.LaunchpadStatus", "name": "current", "type": "uint8"}, {"internalType": "enum ICourseLaunchpad.LaunchpadStatus", "name": "required", "type": "uint8"}], "name": "InvalidStatus", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "InvalidToken", "type": "error"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}], "name": "LaunchpadAlreadyExists", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"inputs": [{"internalType": "string", "name": "reason", "type": "string"}], "name": "TransactionFailed", "type": "error"}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address"}, {"internalType": "string", "name": "reason", "type": "string"}], "name": "Unauthorized", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "launchpadId", "type": "string"}, {"indexed": true, "internalType": "address", "name": "actor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "actionType", "type": "string"}], "name": "FundingAction", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "launchpadId", "type": "string"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "goal", "type": "uint256"}], "name": "LaunchpadCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "launchpadId", "type": "string"}, {"indexed": false, "internalType": "enum ICourseLaunchpad.LaunchpadStatus", "name": "oldStatus", "type": "uint8"}, {"indexed": false, "internalType": "enum ICourseLaunchpad.LaunchpadStatus", "name": "newStatus", "type": "uint8"}], "name": "LaunchpadStatusUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferStarted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "launchpadId", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "availableClaim", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "successful", "type": "bool"}], "name": "VotingResult", "type": "event"}, {"inputs": [{"internalType": "string", "name": "_value", "type": "string"}], "name": "_removeRefundingLaunchpad", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}], "name": "acceptFunding", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "acceptOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "addAcceptedToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}], "name": "approveLaunchpad", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}], "name": "cancelLaunchpad", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}], "name": "claimFunding", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}, {"internalType": "uint256", "name": "availableClaim", "type": "uint256"}], "name": "completeVotingPhase", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}, {"internalType": "enum ICourseLaunchpad.LaunchpadStatus", "name": "status", "type": "uint8"}], "name": "emergencyChangeStatus", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}], "name": "endFundingResult", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}, {"internalType": "bool", "name": "isSuccessful", "type": "bool"}], "name": "endLaunchpad", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getAllRefundingLaunchpads", "outputs": [{"internalType": "string[]", "name": "", "type": "string[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}, {"internalType": "address", "name": "backer", "type": "address"}], "name": "getBackerBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}], "name": "getLaunchpad", "outputs": [{"components": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "goal", "type": "uint256"}, {"internalType": "uint256", "name": "totalPledged", "type": "uint256"}, {"internalType": "uint256", "name": "raised", "type": "uint256"}, {"internalType": "uint256", "name": "availableClaim", "type": "uint256"}, {"internalType": "uint256", "name": "startFundingTime", "type": "uint256"}, {"internalType": "uint256", "name": "endFundingTime", "type": "uint256"}, {"internalType": "uint256", "name": "stakeAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minPledgeAmount", "type": "uint256"}, {"internalType": "enum ICourseLaunchpad.LaunchpadStatus", "name": "status", "type": "uint8"}], "internalType": "struct ICourseLaunchpad.Launchpad", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}, {"internalType": "address", "name": "launchpadOwner", "type": "address"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "goal", "type": "uint256"}, {"internalType": "uint256", "name": "minPledgeAmount", "type": "uint256"}], "name": "initLaunchpad", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pending<PERSON><PERSON>er", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "pledgeERC20", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "fee", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "pledgeERC20withPermit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}], "name": "pledgeNative", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}, {"internalType": "address", "name": "refundContract", "type": "address"}, {"internalType": "bytes32", "name": "receiversRoot", "type": "bytes32"}], "name": "refundLaunchpad", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}], "name": "rejectLaunchpad", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "removeAcceptedToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "maxFundingBps", "type": "uint256"}], "name": "setMaxFundingBps", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "maxFundingDuration", "type": "uint256"}], "name": "setMaxFundingDuration", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "maxPledgeBps", "type": "uint256"}], "name": "setMaxPledgeBps", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requiredStakeAmount", "type": "uint256"}], "name": "setRequiredStakeAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requiredVotingBps", "type": "uint256"}], "name": "setRequiredVotingBps", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "launchpadId", "type": "string"}, {"internalType": "uint256", "name": "startFundingTime", "type": "uint256"}, {"internalType": "uint256", "name": "endFundingTime", "type": "uint256"}], "name": "startFunding", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]