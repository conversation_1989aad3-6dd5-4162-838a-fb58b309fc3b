package log

import (
	"fmt"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"openedu-blockchain/pkg/util"
	"os"
	"path"
	"strings"
)

const (
	DebugLevel  = "debug"
	InfoLevel   = "info"
	WarnLevel   = "warn"
	ErrorLevel  = "error"
	DPanicLevel = "dpanic"
	PanicLevel  = "panic"
	FatalLevel  = "fatal"

	DevMode  = "dev"
	ProdMode = "pro"

	EncodingConsole = "console"
	EncodingJSON    = "json"

	ZapTypeSugar
)

type ConfigLogger struct {
	Mode              string `yaml:"mode" mapstructure:"mode"`
	DisableCaller     bool   `yaml:"disable_caller" mapstructure:"disable_caller"`
	DisableStacktrace bool   `yaml:"disable_stacktrace" mapstructure:"disable_stacktrace"`
	Encoding          string `yaml:"encoding" mapstructure:"encoding"`
	Level             string `yaml:"level" mapstructure:"level"`
	ZapType           string `yaml:"zap_type" mapstructure:"zap_type"`
	SavePath          string `yaml:"save_path" mapstructure:"save_path"`
	SaveName          string `yaml:"save_name" mapstructure:"save_name"`
	SaveTimeFormat    string `yaml:"save_time_format" mapstructure:"save_time_format"`
	FileExt           string `yaml:"file_ext" mapstructure:"file_ext"`
	FileMaxSizeInMB   int    `yaml:"file_max_size_mb" mapstructure:"file_max_size_mb"`
	FileMaxAgeInDays  int    `yaml:"file_max_age_days" mapstructure:"file_max_age_days"`
	FileMaxBackups    int    `yaml:"file_max_backups" mapstructure:"file_max_backups"`
	Compress          bool   `yaml:"compress" mapstructure:"compress"`
}

var loggerLevelMap = map[string]zapcore.Level{
	DebugLevel:  zapcore.DebugLevel,
	InfoLevel:   zapcore.InfoLevel,
	WarnLevel:   zapcore.WarnLevel,
	ErrorLevel:  zapcore.ErrorLevel,
	DPanicLevel: zapcore.DPanicLevel,
	PanicLevel:  zapcore.PanicLevel,
	FatalLevel:  zapcore.FatalLevel,
}

type Logger struct {
	SugarLogger *zap.SugaredLogger
	Logger      *zap.Logger
	key         string
	zapSugar    bool
}

func (l *Logger) Error(args ...interface{}) {
	if l.zapSugar {
		l.SugarLogger.Error(args...)
		return
	}
	str := fmt.Sprintf("%s", args...)
	fields := []zapcore.Field{
		zap.String("UUID", l.key),
	}
	l.Logger.Error(str, fields...)
}

func (l *Logger) Errorf(template string, args ...interface{}) {
	if l.zapSugar {
		str := fmt.Sprintf("UUID:%s, %s", l.key, template)
		l.SugarLogger.Errorf(str, args...)
		return
	}
	fields := []zapcore.Field{
		zap.String("UUID", l.key),
	}
	l.Logger.Error(fmt.Sprintf(template, args...), fields...)
}

var defaultLogger = &Logger{}
var ginResponseLogger = &Logger{}

func GinRespLogger() *Logger {
	return ginResponseLogger
}

func newZapWriteSyncer(cfg *ConfigLogger) zapcore.WriteSyncer {
	filePath := getLogFilePath(cfg)
	fileName := getLogFileName(cfg)
	w := zapcore.AddSync(&lumberjack.Logger{
		Filename:   path.Join(filePath, fileName),
		MaxSize:    cfg.FileMaxSizeInMB,  // megabytes
		MaxAge:     cfg.FileMaxAgeInDays, // days
		MaxBackups: cfg.FileMaxBackups,
		Compress:   cfg.Compress,
	})

	return zapcore.NewMultiWriteSyncer(
		zapcore.AddSync(os.Stderr),
		zapcore.AddSync(w),
	)
}

// Setup initialize the Logger instance
// Ref: https://github.com/ducnpdev/golang-demo/tree/logger
func Setup(cfg *ConfigLogger) {
	logLevel, exist := loggerLevelMap[cfg.Level]
	if !exist {
		logLevel = zapcore.DebugLevel
	}

	var encoderCfg zapcore.EncoderConfig
	if cfg.Mode == ProdMode {
		encoderCfg = zap.NewProductionEncoderConfig()
	} else {
		encoderCfg = zap.NewDevelopmentEncoderConfig()
	}

	encoderCfg.LevelKey = "LEVEL"
	encoderCfg.CallerKey = "CALLER"
	encoderCfg.TimeKey = "TIME"
	encoderCfg.NameKey = "NAME"
	encoderCfg.MessageKey = "MESSAGE"
	encoderCfg.EncodeDuration = zapcore.SecondsDurationEncoder
	encoderCfg.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderCfg.EncodeCaller = zapcore.ShortCallerEncoder
	encoderCfg.EncodeLevel = zapcore.CapitalLevelEncoder
	encoderCfg.FunctionKey = "FUNC"
	var encoder zapcore.Encoder
	if cfg.Encoding == EncodingConsole {
		encoder = zapcore.NewConsoleEncoder(encoderCfg)
	} else {
		encoder = zapcore.NewJSONEncoder(encoderCfg)
	}

	core := zapcore.NewCore(encoder, newZapWriteSyncer(cfg), zap.NewAtomicLevelAt(logLevel))
	zapLogger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(2))
	sugarLogger := zapLogger.Sugar()

	logging := &Logger{
		SugarLogger: sugarLogger,
		Logger:      zapLogger,
		key:         util.GenerateId(),
		zapSugar:    strings.Contains(cfg.ZapType, ZapTypeSugar),
	}

	defaultLogger = logging

	gLoggerZap := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(3))
	gSugarLogger := gLoggerZap.Sugar()
	gLogging := &Logger{
		SugarLogger: gSugarLogger,
		Logger:      gLoggerZap,
		key:         util.GenerateId(),
		zapSugar:    strings.Contains(cfg.ZapType, "sugar"),
	}

	ginResponseLogger = gLogging
}

func SetLogID(key string) {
	defaultLogger.key = key
}

func Debug(args ...interface{}) {
	if defaultLogger.zapSugar {
		defaultLogger.SugarLogger.Debug(args...)
		return
	}
	str := fmt.Sprintf("%s", args...)
	fields := []zapcore.Field{
		zap.String("UUID", defaultLogger.key),
	}
	defaultLogger.Logger.Debug(str, fields...)
}

func Debugf(template string, args ...interface{}) {
	if defaultLogger.zapSugar {
		str := fmt.Sprintf("UUID:%s, %s", defaultLogger.key, template)
		defaultLogger.SugarLogger.Debugf(str, args...)
		return
	}
	str := fmt.Sprintf("%s", args...)
	fields := []zapcore.Field{
		zap.String("UUID", defaultLogger.key),
	}
	defaultLogger.Logger.Debug(str, fields...)
}

func Info(args ...interface{}) {
	if defaultLogger.zapSugar {
		defaultLogger.SugarLogger.Info(args...)
		return
	}
	str := fmt.Sprintf("%s", args...)
	fields := []zapcore.Field{
		zap.String("UUID", defaultLogger.key),
	}
	defaultLogger.Logger.Info(str, fields...)
}

func Infof(template string, args ...interface{}) {
	if defaultLogger.zapSugar {
		str := fmt.Sprintf("UUID:%s, %s", defaultLogger.key, template)
		defaultLogger.SugarLogger.Infof(str, args...)
		return
	}
	fields := []zapcore.Field{
		zap.String("UUID", defaultLogger.key),
	}
	defaultLogger.Logger.Info(fmt.Sprintf(template, args...), fields...)
}

func Warn(args ...interface{}) {
	defaultLogger.SugarLogger.Warn(args...)
}

func Warnf(template string, args ...interface{}) {
	defaultLogger.SugarLogger.Warnf(template, args...)
}

func Error(args ...interface{}) {
	defaultLogger.Error(args...)
}

func Errorf(template string, args ...interface{}) {
	defaultLogger.Errorf(template, args...)
}

func DPanic(args ...interface{}) {
	defaultLogger.SugarLogger.DPanic(args...)
}

func LoggerDPanicf(template string, args ...interface{}) {
	defaultLogger.SugarLogger.DPanicf(template, args...)
}

func Panic(args ...interface{}) {
	defaultLogger.SugarLogger.Panic(args...)
}

func Panicf(template string, args ...interface{}) {
	defaultLogger.SugarLogger.Panicf(template, args...)
}

func Fatal(args ...interface{}) {
	defaultLogger.SugarLogger.Fatal(args...)
}

func Fatalf(template string, args ...interface{}) {
	defaultLogger.SugarLogger.Fatalf(template, args...)
}
