package cache_clients

import (
	"openedu-blockchain/pkg/cache"
	"time"
)

const (
	SystemConfigTTL = 48 * time.Hour
)

func (c *SystemConfigCache) Set(systemKey string, system interface{}) error {
	key := makeCacheKey(c.Prefix, systemKey)
	return cache.Client.Set(key, system, SystemConfigTTL)
}

func (c *SystemConfigCache) Get(cfgKey string, config interface{}) error {
	key := makeCacheKey(c.Prefix, cfgKey)
	return cache.Client.Get(key, config)
}

func (c *SystemConfigCache) SetAll(cacheKey string, systems []interface{}) error {
	key := makeCacheKey(c.Prefix, cacheKey)
	return cache.Client.Set(key, systems, SystemConfigTTL)
}

func (c *SystemConfigCache) GetAll(cacheKey string, systems *[]interface{}) error {
	key := makeCacheKey(c.Prefix, cacheKey)
	return cache.Client.Get(key, systems)
}

func (c *SystemConfigCache) Delete(cfgKey string) error {
	key := makeCacheKey(c.Prefix, cfgKey)
	return cache.Client.Delete(key)
}

func (c *SystemConfigCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *SystemConfigCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
