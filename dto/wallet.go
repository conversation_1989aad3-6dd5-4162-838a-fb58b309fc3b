package dto

import (
	"github.com/shopspring/decimal"
	"openedu-blockchain/models"
)

type WalletRequest struct {
	UserID       string                   `json:"user_id" binding:"required"`
	Network      models.BlockchainNetwork `json:"network" binding:"required,blockchain_network"`
	CoreWalletID string                   `json:"core_wallet_id"`
}

type ListWalletsResponse struct {
	Results    []*models.SimpleWallet `json:"results"`
	Pagination *models.Pagination     `json:"pagination"`
}

type WalletSyncResponse struct {
	ID           string                   `json:"id"`
	UserID       string                   `json:"user_id"`
	Address      string                   `json:"address"`
	PublicKey    string                   `json:"public_key"`
	Network      models.BlockchainNetwork `json:"network"`
	Status       models.WalletStatus      `json:"status"`
	Balance      decimal.Decimal          `json:"balance"`
	CoreWalletID string                   `json:"core_wallet_id"`
}

type GetWalletEarningsRequest struct {
	UserID    string `json:"user_id"`
	IsMainnet bool   `json:"is_mainnet"`
}

type WalletEarningResponse struct {
	BlockchainWalletID string                   `json:"blockchain_wallet_id"`
	CoreWalletID       string                   `json:"core_wallet_id"`
	Address            string                   `json:"address"`
	Network            models.BlockchainNetwork `json:"network"`
	Token              models.BlockchainToken   `json:"token"`
	TokenID            string                   `json:"token_id"`
	Amount             decimal.Decimal          `json:"amount"`
}

type GetWalletGasSponsorBalanceRequest struct {
	WalletID   string `json:"wallet_id"`
	CourseCuid string `json:"course_cuid"`
	IsMainnet  bool   `json:"is_mainnet"`
}

type GetAccountInfoRequest struct {
	Network   models.BlockchainNetwork `json:"network"`
	Address   string                   `json:"address"`
	IsMainnet bool                     `json:"is_mainnet"`
}

type GetAccountInfoResponse struct {
	Network     models.BlockchainNetwork `json:"network"`
	Address     string                   `json:"address"`
	AccountInfo interface{}              `json:"account_info"`
}

type ListSponsorWalletsResponse struct {
	Results    []*models.SanitizedSponsorWallet `json:"results"`
	Pagination *models.Pagination               `json:"pagination"`
}

type CreateSponsorWalletResponse struct {
	Transaction   *models.Transaction   `json:"transaction"`
	SponsorWallet *models.SponsorWallet `json:"sponsor_wallet"`
}
