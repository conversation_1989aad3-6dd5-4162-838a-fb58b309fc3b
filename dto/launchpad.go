package dto

import (
	"github.com/shopspring/decimal"
	"openedu-blockchain/models"
)

type GetVotingPowersRequest struct {
	PoolID    string                   `json:"pool_id"`
	Network   models.BlockchainNetwork `json:"network"`
	IsMainnet bool                     `json:"is_mainnet"`
}

type VotingPowerEntry struct {
	UserID      string          `json:"user_id"`
	Address     string          `json:"address"`
	Amount      decimal.Decimal `json:"amount"`
	TotalAmount decimal.Decimal `json:"total_amount"`
	VotingPower float64         `json:"voting_power"`
}

type GetVotingPowersResponse struct {
	VotingPowers []*VotingPowerEntry `json:"voting_powers"`
}
