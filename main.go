package main

import (
	"context"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/samber/lo"
	"net/http"
	"openedu-blockchain/api"
	"openedu-blockchain/config"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/kms"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/openedu_core"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/queues"
	"openedu-blockchain/services"
	"openedu-blockchain/validator"
	"os"
	"os/signal"
	"strconv"
	"time"
)

var _ binding.StructValidator = validator.New()

func init() {
	setting.Setup()

	log.Setup(&log.ConfigLogger{
		Mode:     log.DevMode,
		Encoding: log.EncodingConsole,
		ZapType:  log.ZapTypeSugar,
		Level: lo.If(
			setting.ServerSetting.RunMode == setting.RunModeRelease,
			log.InfoLevel,
		).Else(log.DebugLevel),
		SavePath:         setting.AppSetting.LogSavePath,
		SaveName:         setting.AppSetting.LogSaveName,
		SaveTimeFormat:   setting.AppSetting.LogSaveTimeFormat,
		FileExt:          setting.AppSetting.LogFileExt,
		FileMaxSizeInMB:  setting.AppSetting.LogFileMaxSizeInMB,
		FileMaxAgeInDays: setting.AppSetting.LogFileMaxAgeInDays,
		FileMaxBackups:   setting.AppSetting.LogFileMaxBackups,
		Compress:         setting.AppSetting.LogCompressEnabled,
	})
	kms.Setup(kms.KeyManagerConfig{
		Provider:     kms.AwsKMS,
		KeyID:        setting.AppSetting.KeyManagerKeyID,
		AwsAccessKey: setting.AwsSetting.AccessKey,
		AwsSecretKey: setting.AwsSetting.SecretKey,
		AwsRegion:    setting.AwsSetting.Region,
	})
	models.Setup()
	services.Setup()
	openedu_core.Setup()
}

// @title						OpenEdu Chain APIs
// @version						1.0
// @description					The API server for OpenEdu Blockchain Service.
// @securityDefinitions.apikey	ApiKeyAuth
// @in							header
// @name						X-api-key
// @description				    Enter the API key in the format "{API key}"
func main() {
	gin.SetMode(setting.ServerSetting.RunMode)
	binding.Validator = validator.New()
	routersInit := api.InitRouter()
	readTimeout := setting.ServerSetting.ReadTimeout
	writeTimeout := setting.ServerSetting.WriteTimeout
	port := setting.ServerSetting.HttpPort
	portStr := os.Getenv("PORT")
	if portStr != "" {
		port, _ = strconv.Atoi(portStr)
	}

	endPoint := fmt.Sprintf("%s:%d", setting.ServerSetting.Host, port)
	maxHeaderBytes := 1 << 20

	server := &http.Server{
		Addr:           endPoint,
		Handler:        routersInit,
		ReadTimeout:    readTimeout,
		WriteTimeout:   writeTimeout,
		MaxHeaderBytes: maxHeaderBytes,
	}

	models.MigrateDatabase()

	config.InitSystem()

	go func() {
		log.Info("Start http server listening ", endPoint)
		if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatalf("listen: %s\n", err)
		}
	}()

	log.Info("Start consuming messages on queues")
	go func() {
		if err := queues.Consume(); err != nil {
			log.Fatalf("Consume messages on queues failed: %v", err)
		}
	}()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt)
	<-quit
	log.Info("Shutting down server...")

	if err := queues.Stop(); err != nil {
		log.Fatalf("Stop queues failed: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}
	log.Info("Server exiting")
}
