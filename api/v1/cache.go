package v1

import (
	"github.com/gin-gonic/gin"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/app"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/setting"
	"strings"
)

// DeleteCaches
//
//	@Summary		Delete all caches
//	@Description	Delete all caches
//
//	@Tags			cache
//	@Accept			json
//	@Produce		json
//	@Security		ApiKeyAuth
//
//	@Success		200			{object}	app.ResponseT[string]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/caches [DELETE]
func DeleteCaches(c *gin.Context) {
	appG := app.Gin{C: c}
	if err := models.Cache.DeleteAll(); err != nil {
		appG.Response500(e.CacheDeleteAllFailed, err.Error())
		return
	}
	appG.Response200("success")
}

// DeleteCacheByKey
//
//	@Summary		Delete cache by key
//	@Description	Delete cache by key
//
//	@Tags			cache
//	@Accept			json
//	@Produce		json
//	@Security		ApiKeyAuth
//
//	@Success		200			{object}	app.ResponseT[string]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/caches/{key} [DELETE]
func DeleteCacheByKey(c *gin.Context) {
	appG := app.Gin{C: c}
	cacheKey := c.Param("key")
	var err error
	switch {
	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.SystemConfigCachePrefix):
		err = models.Cache.System.DeleteByKey(cacheKey)
	}

	if err != nil {
		appG.Response500(e.CacheDeleteByKeyFailed, err.Error())
		return
	}
	appG.Response200("success")
}
