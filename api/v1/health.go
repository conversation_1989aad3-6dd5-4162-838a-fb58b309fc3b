package v1

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"openedu-blockchain/pkg/app"
	"openedu-blockchain/pkg/setting"
	"os"
	"strconv"
)

func CheckHealth(c *gin.Context) {
	appG := app.Gin{C: c}
	buildName := setting.ServerSetting.BuildName
	port := setting.ServerSetting.HttpPort
	portStr := os.Getenv("PORT")
	if portStr != "" {
		port, _ = strconv.Atoi(portStr)
	}
	appG.C.JSON(200, fmt.Sprintf("api v1.0.0, build %s running on port: %d", buildName, port))
	appG.C.Next()
	return
}
